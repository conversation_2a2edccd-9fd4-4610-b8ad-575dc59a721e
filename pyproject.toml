[project]
name = "network-timestamp-fetcher"
version = "0.1.0"
description = "Fetch network timestamps for vehicle VINs"
requires-python = "==3.11.10"
dependencies = [
    "aiohttp>=3.11.11",
    "apscheduler>=3.11.0",
    "atlassian-python-api>=3.41.16",
    "google-api-python-client>=2.157.0",
    "google-auth-httplib2>=0.2.0",
    "google-auth-oauthlib>=1.2.1",
    "loguru>=0.7.3",
    "openpyxl>=3.1.5",
    "pandas",
    "portalocker>=3.1.1",
    "pyinstaller>=6.11.1",
    "python-dotenv>=1.0.1",
    "requests",
    "rich>=13.9.4",
    "typer>=0.15.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.rye]
managed = true
dev-dependencies = [] 

[dependency-groups]
dev = [
    "ruff>=0.11.10",
]
