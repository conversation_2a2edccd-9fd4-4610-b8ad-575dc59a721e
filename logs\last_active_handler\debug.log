2025-06-10 17:11:11.653 | INFO     | src.last_active_handler:<module>:23 - <PERSON><PERSON> configured. CWD: /Users/<USER>/codes/olae_enterprise_git/telematics_command_center_nw_checker. Log directory: /Users/<USER>/codes/olae_enterprise_git/telematics_command_center_nw_checker/logs/last_active_handler
2025-06-10 17:11:11.654 | INFO     | __main__:main_ladh_test:302 - Starting standalone test for last_available_data_handler with VIN: P53AWDCC2CEA00079
2025-06-10 17:11:11.654 | INFO     | __main__:main_ladh_test:307 - --- Fetching last active timestamp for P53AWDCC2CEA00079 ---
2025-06-10 17:11:11.654 | INFO     | src.last_active_handler:fetch_last_active_details:129 - Fetching last active details for VIN: P53AWDCC2CEA00079 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS?vehicleId=P53AWDCC2CEA00079 with max 2 retries.
2025-06-10 17:11:11.654 | DEBUG    | src.last_active_handler:_async_fetch_with_retry:75 - Attempting API call
2025-06-10 17:11:12.131 | INFO     | src.last_active_handler:_async_fetch_with_retry:85 - API call successful
2025-06-10 17:11:12.142 | INFO     | __main__:main_ladh_test:321 - Using actual last active timestamp for P53AWDCC2CEA00079: 2025-06-09 16:22:44 (1749486164000ms epoch) as reference.
2025-06-10 17:11:12.143 | INFO     | __main__:main_ladh_test:324 - --- Testing Version Fetch ---
2025-06-10 17:11:12.143 | INFO     | __main__:fetch_last_available_versions:183 - Attempting to fetch last available versions for VIN P53AWDCC2CEA00079
2025-06-10 17:11:12.143 | INFO     | __main__:_fetch_raw_version_info:133 - Fetching raw version info for VIN P53AWDCC2CEA00079 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/vehicle/versioninfo
2025-06-10 17:11:12.143 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 17:11:12.229 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 17:11:12.230 | INFO     | __main__:main_ladh_test:326 - Fetched versions for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'vehicle_software_version': '5.0.1.gen3.0514', 'move_os_version': '7.1.12', 'platform_config_version': '4.7.2', 'app_config_version': '7.1.24', 'services_config_version': '7.1.25', 'ecu_config_version': '4.7.2', 'bcm_ble_version': '5.2.4', 'hmi_version': '7.1.61', 'otam_version': '7.1.13', 'firmware_version': '7', 'hardware_version': '5', 'scooter_variant': '26.0', 'battery_id': '', 'scooter_id': 'ORQGKNYPMZMFC6MJ', 'som_id': 'ORQGKNYPMZMFC6MJ', 'ecu_version': '{"{\\"sw_bsw_version\\":\\"66 13 05 L\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bms_hardware_variant\\":\\"1\\",\\"bms_dipswitch_variant\\":\\"1\\"},\\"ecu_type\\":\\"MASTER_BMS\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"64 05 01\\"}","{\\"sw_bsw_version\\":\\"52 02 83 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"225 123 127\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"MCU\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"L- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_L\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"R- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_R\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"88 09 62\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bcm_hardware_variant\\":\\"4\\"},\\"ecu_type\\":\\"BCM\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"71 03 01\\"}","{\\"sw_bsw_version\\":\\"0 00 00\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"50 01 01\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HU\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"0 00 00\\"}","{\\"generic_version_details\\":{\\"som_hardware_variant\\":\\"\\",\\"som_resolution\\":\\"0\\"},\\"ecu_type\\":\\"SM\\"}","{\\"sw_bsw_version\\":\\"50 07 00 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"1\\",\\"generic_version_details\\":{\\"HCU_Identification\\":\\"1.0\\",\\"HCU_Model\\":\\"1.0\\"},\\"ecu_type\\":\\"ABS\\",\\"ecu_serial_number\\":\\"\\"}"}', 'timestamp_of_data': '2025-06-09 16:17:09.524', 'api_status_code': 200, 'api_message': 'Success', 'data_found': True}
2025-06-10 17:11:13.231 | INFO     | __main__:main_ladh_test:331 - --- Testing Location Fetch ---
2025-06-10 17:11:13.232 | INFO     | __main__:fetch_last_available_location:235 - Attempting to fetch last available location for VIN P53AWDCC2CEA00079 around 1749486164000
2025-06-10 17:11:13.232 | INFO     | __main__:fetch_last_available_location:254 - Stage 1 ('1_hour'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 15:22:44+00:00 to 2025-06-09 16:22:44+00:00 with 2 retries.
2025-06-10 17:11:13.233 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749482564000-1749486164000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 17:11:13.233 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 17:11:13.945 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 17:11:13.945 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '1_hour'. Raw response: {'statusCode': 204, 'message': 'No data found', 'data': None, 'totalRecordCount': 0, 'timestamp': '1749555674081', 'timeStamp': '1749555674081'}
2025-06-10 17:11:13.945 | INFO     | __main__:fetch_last_available_location:254 - Stage 2 ('6_hours'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 10:22:44+00:00 to 2025-06-09 16:22:44+00:00 with 3 retries.
2025-06-10 17:11:13.945 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749464564000-1749486164000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 17:11:13.945 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 17:11:16.787 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 17:11:16.787 | INFO     | __main__:fetch_last_available_location:272 - Found GPS data for VIN P53AWDCC2CEA00079 at stage '6_hours': lat=12.303469, lon=78.40742, ts=2025-06-09 16:21:39.521
2025-06-10 17:11:16.787 | INFO     | __main__:_reverse_geocode_location:155 - Reverse geocoding for lat: 12.303469, lon: 78.40742
2025-06-10 17:11:16.787 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 17:11:17.585 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 17:11:17.585 | INFO     | __main__:main_ladh_test:334 - Fetched location for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'latitude': '12.303469', 'longitude': '78.40742', 'formatted_address': 'Tamil Nadu', 'timestamp_of_data': '2025-06-09 16:21:39.521', 'data_found': True, 'stage_found': '6_hours', 'api_status_code': 200, 'api_message': 'Success'}
2025-06-10 17:11:17.586 | INFO     | __main__:main_ladh_test:336 - Standalone test for last_available_data_handler finished.
2025-06-10 19:57:02.929 | INFO     | src.last_active_handler:<module>:23 - Logger configured. CWD: /Users/<USER>/codes/olae_enterprise_git/telematics_command_center_nw_checker. Log directory: /Users/<USER>/codes/olae_enterprise_git/telematics_command_center_nw_checker/logs/last_active_handler
2025-06-10 19:57:02.929 | INFO     | __main__:main_ladh_test:302 - Starting standalone test for last_available_data_handler with VIN: P53AWDCC2CEA00079
2025-06-10 19:57:02.929 | INFO     | __main__:main_ladh_test:307 - --- Fetching last active timestamp for P53AWDCC2CEA00079 ---
2025-06-10 19:57:02.930 | INFO     | src.last_active_handler:fetch_last_active_details:129 - Fetching last active details for VIN: P53AWDCC2CEA00079 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS?vehicleId=P53AWDCC2CEA00079 with max 2 retries.
2025-06-10 19:57:02.930 | DEBUG    | src.last_active_handler:_async_fetch_with_retry:75 - Attempting API call
2025-06-10 19:57:03.317 | INFO     | src.last_active_handler:_async_fetch_with_retry:85 - API call successful
2025-06-10 19:57:03.320 | INFO     | __main__:main_ladh_test:321 - Using actual last active timestamp for P53AWDCC2CEA00079: 2025-06-09 16:22:44 (1749486164000ms epoch) as reference.
2025-06-10 19:57:03.320 | INFO     | __main__:main_ladh_test:324 - --- Testing Version Fetch ---
2025-06-10 19:57:03.320 | INFO     | __main__:fetch_last_available_versions:183 - Attempting to fetch last available versions for VIN P53AWDCC2CEA00079
2025-06-10 19:57:03.320 | INFO     | __main__:_fetch_raw_version_info:133 - Fetching raw version info for VIN P53AWDCC2CEA00079 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/vehicle/versioninfo
2025-06-10 19:57:03.320 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 19:57:03.454 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 19:57:03.455 | INFO     | __main__:main_ladh_test:326 - Fetched versions for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'vehicle_software_version': '5.0.1.gen3.0514', 'move_os_version': '7.1.12', 'platform_config_version': '4.7.2', 'app_config_version': '7.1.24', 'services_config_version': '7.1.25', 'ecu_config_version': '4.7.2', 'bcm_ble_version': '5.2.4', 'hmi_version': '7.1.61', 'otam_version': '7.1.13', 'firmware_version': '7', 'hardware_version': '5', 'scooter_variant': '26.0', 'battery_id': '', 'scooter_id': 'ORQGKNYPMZMFC6MJ', 'som_id': 'ORQGKNYPMZMFC6MJ', 'ecu_version': '{"{\\"sw_bsw_version\\":\\"66 13 05 L\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bms_hardware_variant\\":\\"1\\",\\"bms_dipswitch_variant\\":\\"1\\"},\\"ecu_type\\":\\"MASTER_BMS\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"64 05 01\\"}","{\\"sw_bsw_version\\":\\"52 02 83 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"225 123 127\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"MCU\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"L- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_L\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"R- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_R\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"88 09 62\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bcm_hardware_variant\\":\\"4\\"},\\"ecu_type\\":\\"BCM\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"71 03 01\\"}","{\\"sw_bsw_version\\":\\"0 00 00\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"50 01 01\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HU\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"0 00 00\\"}","{\\"generic_version_details\\":{\\"som_hardware_variant\\":\\"\\",\\"som_resolution\\":\\"0\\"},\\"ecu_type\\":\\"SM\\"}","{\\"sw_bsw_version\\":\\"50 07 00 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"1\\",\\"generic_version_details\\":{\\"HCU_Identification\\":\\"1.0\\",\\"HCU_Model\\":\\"1.0\\"},\\"ecu_type\\":\\"ABS\\",\\"ecu_serial_number\\":\\"\\"}"}', 'timestamp_of_data': '2025-06-09 16:17:09.524', 'api_status_code': 200, 'api_message': 'Success', 'data_found': True}
2025-06-10 19:57:04.457 | INFO     | __main__:main_ladh_test:331 - --- Testing Location Fetch ---
2025-06-10 19:57:04.458 | INFO     | __main__:fetch_last_available_location:235 - Attempting to fetch last available location for VIN P53AWDCC2CEA00079 around 1749486164000
2025-06-10 19:57:04.459 | INFO     | __main__:fetch_last_available_location:254 - Stage 1 ('1_hour'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 15:22:44+00:00 to 2025-06-09 16:22:44+00:00 with 2 retries.
2025-06-10 19:57:04.459 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749482564000-1749486164000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 19:57:04.459 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 19:57:04.921 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 19:57:04.921 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '1_hour'. Raw response: {'statusCode': 204, 'message': 'No data found', 'data': None, 'totalRecordCount': 0, 'timestamp': '1749565624994', 'timeStamp': '1749565624994'}
2025-06-10 19:57:04.922 | INFO     | __main__:fetch_last_available_location:254 - Stage 2 ('6_hours'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 10:22:44+00:00 to 2025-06-09 16:22:44+00:00 with 3 retries.
2025-06-10 19:57:04.922 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749464564000-1749486164000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 19:57:04.922 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 19:57:06.254 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 19:57:06.255 | INFO     | __main__:fetch_last_available_location:272 - Found GPS data for VIN P53AWDCC2CEA00079 at stage '6_hours': lat=12.303469, lon=78.40742, ts=2025-06-09 16:21:39.521
2025-06-10 19:57:06.255 | INFO     | __main__:_reverse_geocode_location:155 - Reverse geocoding for lat: 12.303469, lon: 78.40742
2025-06-10 19:57:06.255 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 19:57:07.432 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 19:57:07.433 | INFO     | __main__:main_ladh_test:334 - Fetched location for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'latitude': '12.303469', 'longitude': '78.40742', 'formatted_address': 'Tamil Nadu', 'timestamp_of_data': '2025-06-09 16:21:39.521', 'data_found': True, 'stage_found': '6_hours', 'api_status_code': 200, 'api_message': 'Success'}
2025-06-10 19:57:07.434 | INFO     | __main__:main_ladh_test:336 - Standalone test for last_available_data_handler finished.
