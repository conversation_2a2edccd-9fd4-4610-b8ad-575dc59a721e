# src/format_handler.py
import json
import re
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from loguru import logger
from src import config as app_config

def standardize_timestamp(raw_timestamp: Any) -> str:
    """
    Converts various timestamp inputs (epoch ms, string formats) to 'YYYY-MM-DD HH:MM:SS'.
    Adapts logic from module.py:928-940.
    """
    if not raw_timestamp:
        return ""

    try:
        # Handle different input types
        if isinstance(raw_timestamp, (int, float)):
            # Assume milliseconds epoch
            dt_obj = datetime.fromtimestamp(raw_timestamp / 1000, tz=timezone.utc)
            return dt_obj.strftime("%Y-%m-%d %H:%M:%S")

        elif isinstance(raw_timestamp, str):
            # Try to parse string timestamps
            dt_obj = None
            formats_to_try = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%S.%f"
            ]

            for fmt in formats_to_try:
                try:
                    dt_obj = datetime.strptime(raw_timestamp, fmt)
                    break
                except ValueError:
                    continue

            if dt_obj is None:
                logger.warning(f"Could not parse timestamp string: {raw_timestamp}")
                return raw_timestamp  # Return as-is if can't parse

            # Ensure UTC timezone if not present
            if dt_obj.tzinfo is None:
                dt_obj = dt_obj.replace(tzinfo=timezone.utc)

            return dt_obj.strftime("%Y-%m-%d %H:%M:%S")

        else:
            logger.warning(f"Unsupported timestamp type: {type(raw_timestamp)}")
            return str(raw_timestamp)

    except Exception as e:
        logger.error(f"Error standardizing timestamp '{raw_timestamp}': {e}")
        return str(raw_timestamp) if raw_timestamp else ""

def format_last_active_for_report(last_active_data: Optional[Dict]) -> Dict:
    """
    Extracts and formats relevant fields from Phase 1 output.
    Ensures last_connected_timestamp is standardized.
    """
    if not last_active_data or not last_active_data.get("data_available"):
        return {
            "last_connected": "",
            "pack_soc": "",
            "scooter_state": "",
            "time_charge_full_sc_1": ""
        }

    return {
        "last_connected": standardize_timestamp(last_active_data.get("timestamp")),
        "pack_soc": last_active_data.get("pack_soc", ""),
        "scooter_state": last_active_data.get("scooter_state", ""),
        "time_charge_full_sc_1": last_active_data.get("time_charge_full_sc_1", "")
    }

def _parse_ecu_version_string(ecu_version_str: str) -> Dict[str, str]:
    """
    Parses the ecu_version string to extract BMS, BCM, and MCU versions.
    Exactly replicates logic from module.py:1051-1112 format_version_info_data method.
    """
    versions = {
        "bms_sw": "",
        "bcm_sw": "",
        "mcu_sw": ""
    }

    if not ecu_version_str:
        return versions

    try:
        # Clean up the ecu_version string exactly as done in module.py
        cleaned_str = ecu_version_str.replace('\\', '')
        cleaned_str = cleaned_str.replace('{"{"', '{"')
        cleaned_str = cleaned_str.replace('"}"}', '"}')

        # Use regex to find JSON objects exactly as done in module.py
        import re
        # Pattern from module.py: r'\{".*?\"}'
        pattern = r'\{.*?\}'
        ecu_jsons = re.findall(pattern, cleaned_str)

        # Debug logging
        logger.debug(f"Cleaned string: {cleaned_str[:200]}...")
        logger.debug(f"Found {len(ecu_jsons)} JSON objects: {[json_obj[:50] + '...' for json_obj in ecu_jsons]}")

        for ecu_json in ecu_jsons:
            try:
                ecu_data = json.loads(ecu_json)
                ecu_type = ecu_data.get('ecu_type', '').upper()
                sw_version = ecu_data.get('sw_bsw_version', '')

                if ecu_type == 'MASTER_BMS':
                    versions["bms_sw"] = sw_version
                elif ecu_type == 'BCM':
                    versions["bcm_sw"] = sw_version
                elif ecu_type == 'MCU':
                    versions["mcu_sw"] = sw_version

            except json.JSONDecodeError as e:
                logger.debug(f"Could not parse ECU JSON: {ecu_json}. Error: {e}")
                continue
            except Exception:
                # Silent continue as in module.py
                continue

    except Exception as e:
        logger.error(f"Error parsing ecu_version string: {e}")

    return versions

def format_versions_for_report(version_data: Optional[Dict]) -> Dict:
    """
    Adapts logic from module.py:1051-1112.
    Extracts vehicle_software_version, bms_sw, bcm_sw, mcu_sw.
    Ensures any associated timestamp is standardized.
    """
    if not version_data or not version_data.get("data_found"):
        return {
            "software_version": "",
            "bms_version": "",
            "bcm_version": "",
            "mcu_version": "",
            "version_timestamp": ""
        }

    # Parse ECU versions from the ecu_version string
    ecu_versions = _parse_ecu_version_string(version_data.get("ecu_version", ""))

    return {
        "software_version": version_data.get("vehicle_software_version", ""),
        "bms_version": ecu_versions["bms_sw"],
        "bcm_version": ecu_versions["bcm_sw"],
        "mcu_version": ecu_versions["mcu_sw"],
        "version_timestamp": standardize_timestamp(version_data.get("timestamp_of_data"))
    }

def format_location_for_report(location_data: Optional[Dict]) -> Dict:
    """
    Extracts formatted location string.
    Ensures any associated timestamp is standardized.
    """
    if not location_data or not location_data.get("data_found"):
        return {
            "location": "",
            "latitude": "",
            "longitude": "",
            "location_timestamp": ""
        }

    return {
        "location": location_data.get("formatted_address", ""),
        "latitude": location_data.get("latitude", ""),
        "longitude": location_data.get("longitude", ""),
        "location_timestamp": standardize_timestamp(location_data.get("timestamp_of_data"))
    }

def assemble_final_report_row(
    vin: str,
    last_active_report_data: Dict,
    versions_report_data: Dict,
    location_report_data: Dict
) -> List:
    """
    Combines formatted data from the above functions into a single list representing a row in the Google Sheet.
    Target Columns: VIN, Location, Software Version, BMS Version, BCM Version, MCU Version, Last Connected
    """
    return [
        vin,
        location_report_data.get("location", ""),
        versions_report_data.get("software_version", ""),
        versions_report_data.get("bms_version", ""),
        versions_report_data.get("bcm_version", ""),
        versions_report_data.get("mcu_version", ""),
        last_active_report_data.get("last_connected", "")
    ]

def get_report_headers() -> List[str]:
    """Returns the header row for the Google Sheet report."""
    return [
        "VIN",
        "Location",
        "Software Version",
        "BMS Version",
        "BCM Version",
        "MCU Version",
        "Last Connected"
    ]

if __name__ == "__main__":
    # Standalone execution for testing
    print("Testing format_handler.py")

    # Test timestamp standardization
    test_timestamps = [
        1749463350000,  # milliseconds epoch
        "2025-06-09 06:59:24.442",
        "2025-06-09T06:59:24Z",
        None,
        ""
    ]

    print("\n--- Testing timestamp standardization ---")
    for ts in test_timestamps:
        result = standardize_timestamp(ts)
        print(f"Input: {ts} -> Output: {result}")

    # Test sample data formatting
    print("\n--- Testing data formatting ---")

    sample_last_active = {
        "timestamp": "2025-06-09 06:59:24",
        "pack_soc": "85",
        "scooter_state": "PARKED",
        "data_available": True
    }

    sample_versions = {
        "vehicle_software_version": "5.0.1.gen3.0514",
        "ecu_version": '{"{\\"sw_bsw_version\\":\\"66 13 05 L\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bms_hardware_variant\\":\\"1\\",\\"bms_dipswitch_variant\\":\\"1\\"},\\"ecu_type\\":\\"MASTER_BMS\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"64 05 01\\"}","{\\"sw_bsw_version\\":\\"52 02 83 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"225 123 127\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"MCU\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"88 09 62\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bcm_hardware_variant\\":\\"4\\"},\\"ecu_type\\":\\"BCM\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"71 03 01\\"}"}',
        "timestamp_of_data": "2025-06-09 06:59:24.442",
        "data_found": True
    }

    sample_location = {
        "formatted_address": "Bangalore, Karnataka",
        "latitude": "12.9716",
        "longitude": "77.5946",
        "timestamp_of_data": "2025-06-09 06:59:24",
        "data_found": True
    }

    formatted_active = format_last_active_for_report(sample_last_active)
    formatted_versions = format_versions_for_report(sample_versions)
    formatted_location = format_location_for_report(sample_location)

    print(f"Formatted last active: {formatted_active}")
    print(f"Formatted versions: {formatted_versions}")
    print(f"Formatted location: {formatted_location}")

    # Test final row assembly
    final_row = assemble_final_report_row("P53AWDCC2CEA00079", formatted_active, formatted_versions, formatted_location)
    headers = get_report_headers()

    print(f"\nHeaders: {headers}")
    print(f"Sample row: {final_row}")

    print("\nformat_handler.py testing completed.")