{"statusCode": 200, "message": "Success", "data": [{"iccid_number": "89918610407093112122", "saf_mv_chg_overvg_heal": "4130.0", "saf_cnt_overvltgchrgperm": "5.0", "som_id": "7429890c21422a", "overall_charge_voltage_limit": "31.0", "saf_mv_undervg_error": "2800.0", "bcm_ble_version": "", "ecu_config_version": "", "hmi_moods": "", "ohs_version": "", "slc_aovercurr_tolchg": "5.0", "limp_home_mode": "", "profile_manager_version": "", "slc_cnt_chrg_restart": "3", "overall_charge_current_limit": "0.0", "charge_battery_temperature_error": "55.0", "motor_temp_cut_off": "", "voltage_diff_for_complimentary_spike": "2000.0", "da_version": "", "bcm_ble_mac_address": "00:00:00:00:00:00", "saf_pct_socregenallowed": "94.0", "mfg_version": "", "escl_lock": "", "packet_type": "", "ola_maps_version": "", "ai_proxy_version": "", "soc_ah_usecap_cell": "4.5", "discharge_battery_temperature_warning": "55.0", "otam_version": "", "firmware_version": "", "ta100_id": "", "hardware_version": "", "ecu_unique_identifier_vcu": "", "som_ble_mac_address": "", "schema_version": "", "discharge_mode_current_limit": "220.0", "sleep_battery_temperature_warning": "60.0", "saf_degc_overtemp_cut_off": "67.0", "hmi_version": "", "saf_mv_chg_overvg_error": "4175.0", "motor_temp_warning": "", "ecu_version": "{\"{\\\"hw_version\\\":\\\"9\\\",\\\"generic_version_details\\\":{},\\\"ecu_mfg_code\\\":\\\"1\\\",\\\"ecu_type\\\":\\\"SLOW_CHARGER\\\",\\\"firmware_version\\\":\\\"A\\\",\\\"ecu_serial_number\\\":\\\"89CF3DD5\\\",\\\"sw_bootloader_version\\\":\\\"8\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"66.12.15.B\\\",\\\"hw_version\\\":\\\"1 0\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"MASTER_BMS\\\",\\\"sw_asw_version\\\":\\\"2025050202\\\",\\\"sw_bootloader_version\\\":\\\"64.03.01\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"16.00.15\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"HU\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"17.00.01\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"BCM\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"15.00.46\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"HMI\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"52.02.77.05\\\",\\\"hw_version\\\":\\\"25118001\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"MCU\\\",\\\"sw_bootloader_version\\\":\\\"01.00.00\\\"}\"}", "saf_cnt_errorcntr_compspike": "10.0", "timestamp": "2025-05-21 13:09:59.0", "discharge_current_limit16p": "220.0", "ai_range_service_version": "", "audit_details": "", "vehicle_type": "M3X", "find_my_scooter": "", "serial_number": "", "ecu_unique_identifier_bms": "", "ai_auto_turn_off_version": "", "slc_min_cvend_timer": "30", "connectivity_service_version": "", "vehicle_identification_number": "P53BXDCCXEAA00018", "pdu_temperature_warning": "95.0", "scooter_id": "x", "cruise_control": "", "vdp_version": "", "packet_id": "", "mcu_temp_error": "", "saf_mv_undervg_resm": "3000.0", "imei_number": "865577079757697", "bcm_vin": "P53BXDCCXEAA00018", "cv_charging_cutoff_timer": "30.0", "mcu_temp_cut_off": "", "lock_unlock_proximity": "", "charge_battery_temperature_warning": "46.0", "partition_timestamp": "2025-05-21 05:30:00.0", "charger_battery_temperature_warning_rise": "10.0", "navigation_app_version": "", "move_os_version": "", "ingestion_timestamp": "2025-05-21 13:16:17.287", "pdu_temperature_error": "110.0", "total_regen_amhr": "0.0", "saf_cnt_errorcntr_shrtcrct": "3.0", "pdu_temperature_cut_off": "115.0", "mcu_temp_warning": "", "regen_batt_temp_min_th": "3.0", "som_wifi_mac_address": "", "escl_version": "", "motor_temp_error": "", "discharge_battery_temperature_warning_rise": "10.0", "country_code": "", "vrs_version": "", "app_config_version": "", "discharger_battery_temperature_error": "62.0", "battery_id": "00", "get_home_mode": "", "vehicle_software_version": "4.7.0", "slc_mv_thdvtg_chrgend_cell": "4130.0", "can_database_version_bms": "10A", "slc_mv_minvg_strtcellbal": "3700.0", "sleep_battery_temperature_warning_rise": "10.0", "platform_config_version": "", "slc_mv_thdvtg_ccend_cell": "4130.0", "can_logger_version": "", "motor_id": "", "battery_wakeup_request": "1.0", "ccm_version": "16.00.15", "scooter_variant": "19.0", "pdu_temperature_warning_delta": "150.0", "cell_type": "1.0", "break_switch_model_version": "", "take_me_home_lights": "", "can_database_version_vcu": "", "convoy_service_version": "", "discharge_current_limit12p": "220.0", "cell_min_deviation_warning": "1000.0", "services_config_version": "", "saf_mv_undervg_warng": "2850.0", "saf_cnt_errorcntr_celldip": "10.0", "saf_degc_temprisepdu": "10.0", "regen_soc_upper_lim": "3.0", "saf_degc_regenallowed": "48.0", "vsm_version": "", "parallel_cnt_cell_string": "18.0", "hill_hold": "", "region_code": "", "bcm_bt_mac_address": "", "timeStamp": "2025-05-21 13:09:59.0"}], "totalRecordCount": 1, "timestamp": "1747814664138", "timeStamp": "1747814664138"}