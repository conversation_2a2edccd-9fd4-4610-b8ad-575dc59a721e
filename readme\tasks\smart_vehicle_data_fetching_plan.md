# Smart Vehicle Data Fetching Plan (async_simple.py)

**Version:** 1.0
**Date:** 2025-06-05

## 1. Objective

To implement a more cost-effective and efficient method for fetching vehicle telematics data. This involves first determining a vehicle's last active timestamp using the `getVehicleInfoBMS` endpoint. If successful, this timestamp is used to define a targeted 24-hour query window for subsequent enabled features. If fetching the last active timestamp fails, the system will fall back to a staged approach (similar to the existing `run_async` logic) to ensure data for enabled secondary features is still attempted.

## 2. Key Components

*   **Modifications to `module.py`:** Enhancements to the `NetworkTimestampChecker` class.
*   **New Script `async_simple.py`:** A new Python script to orchestrate the improved data fetching workflow.

## 3. Detailed Plan

### I. Modifications to `module.py` (Class `NetworkTimestampChecker`)

1.  **New Asynchronous Method: `async_fetch_last_active_info`**
    *   **Purpose:** To fetch basic vehicle information, primarily the last active timestamp.
    *   **Signature:** `async def async_fetch_last_active_info(self, session: aiohttp.ClientSession, vin: str, max_retries_for_stage: int) -> Optional[Dict]:`
    *   **Endpoint URL:** `https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS?vehicleId={vin}`
    *   **Authentication:** None required for this specific endpoint.
    *   **Data to Return:** The method should parse and return a dictionary containing the following fields from the `data[0]` array of the JSON response:
        *   `timestamp` (e.g., "2025-04-17 11:03:34")
        *   `pack_soc`
        *   `scooter_state`
        *   `time_charge_full_sc_1`
    *   **Error Handling:** Will utilize `self.async_fetch_with_retry` for robust API calls.

2.  **New Formatting Method: `format_last_active_info_data`**
    *   **Purpose:** To format the data obtained from `async_fetch_last_active_info` for inclusion in a new Google Sheet.
    *   **Signature:** `def format_last_active_info_data(self, vin: str, data: Optional[Dict]) -> list:`
    *   **Output Columns (Example):** `VIN`, `Last Active Timestamp (Raw)`, `Last Active Timestamp (Converted)`, `Pack SOC`, `Scooter State`, `Time Charge Full SC 1`, `Status Code`, `Message`, `Data Available`.

3.  **Google Sheets Updates**
    *   Modify `update_or_create_result_google_sheet`:
        *   Add logic to recognize and create/update a new sheet titled "Last Active Info".
        *   Use `self.format_last_active_info_data` for populating this new sheet.
    *   Modify `get_default_row`:
        *   Add a case to provide a default row structure for the "Last Active Info" sheet.

### II. New Caller Script: `async_simple.py`

This script will orchestrate the new workflow.

**A. Setup & Initialization:**
1.  **Imports:** `asyncio`, `time`, `datetime`, `NetworkTimestampChecker` (from `module.py`), `aiohttp`, `loguru` (or standard logging).
2.  **Configuration:** Define constants such as `ENABLED_SECONDARY_FEATURES` (list of feature names like 'connectivity', 'gps', 'version_info'), `MAX_RETRIES` for API calls.
3.  **Instantiation:** `checker = NetworkTimestampChecker()`
4.  **Feature Flags:** Set the desired secondary features to be fetched using `checker.set_feature_flags({...})`.
5.  **VINs & Results:**
    *   `vin_numbers = checker.get_vin_numbers()`
    *   `aggregated_results = {}` (to store all fetched data, including last active info and secondary features).
    *   `current_script_time_ms = int(time.time() * 1000)` (for fallback time calculations).

**B. Main Orchestration Logic (Loop per VIN):**

For each `vin` in `vin_numbers`:
1.  Create an `aiohttp.ClientSession()`.
2.  **Fetch Last Active Info:**
    *   Call `last_active_data = await checker.async_fetch_last_active_info(session, vin, MAX_RETRIES)`.
    *   Store the returned dictionary (containing `timestamp`, `pack_soc`, etc.) in `aggregated_results` under a key like `'last_active_info'`.
        *   Example: `aggregated_results.setdefault('last_active_info', {})[vin] = last_active_data`

3.  **Determine Query Strategy for Enabled Secondary Features:**
    *   **If `last_active_data` is valid and contains a timestamp:**
        *   Extract `vehicle_last_active_timestamp_str` from `last_active_data['timestamp']`.
        *   Convert it to epoch milliseconds (`vehicle_last_active_ms`).
        *   Define the **24-hour query window**:
            *   `query_to_time_ms = vehicle_last_active_ms`
            *   `query_from_time_ms = vehicle_last_active_ms - (24 * 60 * 60 * 1000)`
        *   For each feature name in `ENABLED_SECONDARY_FEATURES` (and if enabled in `checker.feature_flags`):
            *   If the feature's fetch method (e.g., `async_fetch_gps_timestamp`) accepts `from_time` and `to_time`:
                `data = await checker.async_fetch_<feature_name>(session, vin, query_from_time_ms, query_to_time_ms, MAX_RETRIES)`
            *   Else (if the feature's fetch method, e.g., `async_fetch_version_info`, does not take time parameters):
                `data = await checker.async_fetch_<feature_name>(session, vin, MAX_RETRIES)`
            *   Store the `data` in `aggregated_results` under the corresponding feature key (e.g., `aggregated_results.setdefault(feature_name, {})[vin] = data`).
    *   **If `last_active_data` is invalid or fetching failed (Fallback Logic):**
        *   Log a warning indicating failure to fetch last active timestamp and initiation of fallback.
        *   Initialize a set of `pending_secondary_features_for_vin` with all `ENABLED_SECONDARY_FEATURES` that are active in `checker.feature_flags`.
        *   Iterate through `checker.STAGES_CONFIG` (from `module.py`):
            *   Let `stage_config` be the current stage's configuration.
            *   Calculate `stage_from_time` based on `stage_config["duration_spec"]` relative to `current_script_time_ms`.
            *   `stage_to_time = current_script_time_ms`.
            *   `retries_for_stage = stage_config["retries"]`.
            *   If `pending_secondary_features_for_vin` is empty, break from this stage loop (all features for this VIN found via fallback).
            *   For each `feature_name` in a copy of `pending_secondary_features_for_vin`:
                *   If the feature's fetch method accepts `from_time` and `to_time`:
                    `data = await checker.async_fetch_<feature_name>(session, vin, stage_from_time, stage_to_time, retries_for_stage)`
                *   Else:
                    `data = await checker.async_fetch_<feature_name>(session, vin, retries_for_stage)`
                *   If `checker._is_actual_data(vin, feature_name, data)` returns `True`:
                    *   Store `data` in `aggregated_results.setdefault(feature_name, {})[vin] = data`.
                    *   Remove `feature_name` from `pending_secondary_features_for_vin`.

**C. Finalization (After processing all VINs):**
1.  Call `checker.save_pickle_data(aggregated_results)`.
2.  Call `checker.update_or_create_result_google_sheet(aggregated_results, checker.master_sheet_id)`.

## 4. Data Storage Summary

*   **Pickle File (`request_results.pickle`):**
    *   The `aggregated_results` dictionary will be saved.
    *   It will contain a top-level key `'last_active_info'` mapping VINs to their data from `getVehicleInfoBMS` (`timestamp`, `pack_soc`, `scooter_state`, `time_charge_full_sc_1`).
    *   It will also contain top-level keys for each enabled secondary feature (e.g., `'connectivity'`, `'gps'`, `'version_info'`), mapping VINs to their respective fetched data.
*   **Google Sheets:**
    *   A **new sheet** titled "Last Active Info" (or similar) will be created/updated, containing columns for VIN and the fields from `getVehicleInfoBMS` (`timestamp`, `pack_soc`, `scooter_state`, `time_charge_full_sc_1`).
    *   Existing sheets for other enabled secondary features (Connectivity, GPS, Version Info, etc.) will continue to be populated as per their data.

## 5. Assumptions

*   The `getVehicleInfoBMS` endpoint is stable and requires no authentication.
*   The structure of `STAGES_CONFIG` in `module.py` is suitable for the fallback logic's time window calculations.
*   The `_is_actual_data` method in `NetworkTimestampChecker` is reliable for determining if fetched data is valid.