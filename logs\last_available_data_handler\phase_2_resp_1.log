2025-06-09 10:28:49.718 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:49.718 | INFO     | __main__:main_ladh_test:326 - Fetched versions for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'vehicle_software_version': '5.0.1.gen3.0514', 'move_os_version': '7.1.12', 'platform_config_version': '4.7.2', 'app_config_version': '7.1.24', 'services_config_version': '7.1.25', 'ecu_config_version': '4.7.2', 'bcm_ble_version': '5.2.4', 'hmi_version': '7.1.61', 'otam_version': '7.1.13', 'firmware_version': '7', 'hardware_version': '5', 'scooter_variant': '26.0', 'battery_id': '', 'scooter_id': 'ORQGKNYPMZMFC6MJ', 'som_id': 'ORQGKNYPMZMFC6MJ', 'ecu_version': '{"{\\"sw_bsw_version\\":\\"66 13 05 L\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bms_hardware_variant\\":\\"1\\",\\"bms_dipswitch_variant\\":\\"1\\"},\\"ecu_type\\":\\"MASTER_BMS\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"64 05 01\\"}","{\\"sw_bsw_version\\":\\"52 02 83 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"225 123 127\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"MCU\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"L- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_L\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"R- 0.0.0.0\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HBC_R\\",\\"ecu_serial_number\\":\\"\\"}","{\\"sw_bsw_version\\":\\"88 09 62\\",\\"ecu_mfg_date\\":\\"\\",\\"generic_version_details\\":{\\"bcm_hardware_variant\\":\\"4\\"},\\"ecu_type\\":\\"BCM\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"71 03 01\\"}","{\\"sw_bsw_version\\":\\"0 00 00\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"50 01 01\\",\\"generic_version_details\\":{},\\"ecu_type\\":\\"HU\\",\\"ecu_serial_number\\":\\"\\",\\"sw_bootloader_version\\":\\"0 00 00\\"}","{\\"generic_version_details\\":{\\"som_hardware_variant\\":\\"\\",\\"som_resolution\\":\\"0\\"},\\"ecu_type\\":\\"SM\\"}","{\\"sw_bsw_version\\":\\"50 07 00 01\\",\\"ecu_mfg_date\\":\\"\\",\\"hw_version\\":\\"1\\",\\"generic_version_details\\":{\\"HCU_Identification\\":\\"1.0\\",\\"HCU_Model\\":\\"1.0\\"},\\"ecu_type\\":\\"ABS\\",\\"ecu_serial_number\\":\\"\\"}"}', 'timestamp_of_data': '2025-06-09 06:59:24.442', 'api_status_code': 200, 'api_message': 'Success', 'data_found': True}
2025-06-09 10:28:50.729 | INFO     | __main__:main_ladh_test:331 - --- Testing Location Fetch ---
2025-06-09 10:28:50.730 | INFO     | __main__:fetch_last_available_location:235 - Attempting to fetch last available location for VIN P53AWDCC2CEA00079 around 1749463350000
2025-06-09 10:28:50.731 | INFO     | __main__:fetch_last_available_location:254 - Stage 1 ('1_hour'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 09:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 2 retries.
2025-06-09 10:28:50.731 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749459750000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.732 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:50.779 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:50.780 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '1_hour'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:50.783 | INFO     | __main__:fetch_last_available_location:254 - Stage 2 ('6_hours'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-09 04:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 3 retries.
2025-06-09 10:28:50.784 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749441750000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.791 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:50.842 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:50.843 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '6_hours'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:50.843 | INFO     | __main__:fetch_last_available_location:254 - Stage 3 ('24_hours'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-08 10:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 4 retries.
2025-06-09 10:28:50.845 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749376950000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.846 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:50.893 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:50.896 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '24_hours'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:50.897 | INFO     | __main__:fetch_last_available_location:254 - Stage 4 ('3_days'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-06-06 10:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 5 retries.
2025-06-09 10:28:50.898 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1749204150000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.899 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:50.947 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:50.947 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '3_days'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:50.948 | INFO     | __main__:fetch_last_available_location:254 - Stage 5 ('1_month'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-05-10 10:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 6 retries.
2025-06-09 10:28:50.948 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1746871350000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.949 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:50.993 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:50.994 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '1_month'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:50.994 | INFO     | __main__:fetch_last_available_location:254 - Stage 6 ('3_months'): Fetching GPS for VIN P53AWDCC2CEA00079 in window 2025-03-11 10:02:30+00:00 to 2025-06-09 10:02:30+00:00 with 7 retries.
2025-06-09 10:28:50.995 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53AWDCC2CEA00079 in range 1741687350000-1749463350000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-09 10:28:50.995 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-09 10:28:51.040 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-09 10:28:51.042 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53AWDCC2CEA00079 at stage '3_months'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-09 10:28:51.042 | WARNING  | __main__:fetch_last_available_location:292 - No GPS data found for VIN P53AWDCC2CEA00079 after all stages.
2025-06-09 10:28:51.046 | INFO     | __main__:main_ladh_test:334 - Fetched location for P53AWDCC2CEA00079: {'vin': 'P53AWDCC2CEA00079', 'data_found': False}
2025-06-09 10:28:51.046 | INFO     | __main__:main_ladh_test:336 - Standalone test for last_available_data_handler finished.