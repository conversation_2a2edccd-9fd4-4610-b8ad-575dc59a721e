025-06-10 20:14:26.222 | INFO     | __main__:main_ladh_test:331 - --- Testing Location Fetch ---
2025-06-10 20:14:26.222 | INFO     | __main__:fetch_last_available_location:235 - Attempting to fetch last available location for VIN P53BXDCC6ECA00005 around 1749585912000
2025-06-10 20:14:26.223 | INFO     | __main__:fetch_last_available_location:254 - Stage 1 ('1_hour'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-06-10 19:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 2 retries.
2025-06-10 20:14:26.223 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1749582312000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.230 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.329 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.329 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '1_hour'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.329 | INFO     | __main__:fetch_last_available_location:254 - Stage 2 ('6_hours'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-06-10 14:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 3 retries.
2025-06-10 20:14:26.329 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1749564312000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.329 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.459 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.459 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '6_hours'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.459 | INFO     | __main__:fetch_last_available_location:254 - Stage 3 ('24_hours'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-06-09 20:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 4 retries.
2025-06-10 20:14:26.459 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1749499512000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.459 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.527 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.528 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '24_hours'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.528 | INFO     | __main__:fetch_last_available_location:254 - Stage 4 ('3_days'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-06-07 20:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 5 retries.
2025-06-10 20:14:26.528 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1749326712000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.528 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.594 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.595 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '3_days'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.595 | INFO     | __main__:fetch_last_available_location:254 - Stage 5 ('1_month'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-05-11 20:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 6 retries.
2025-06-10 20:14:26.596 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1746993912000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.596 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.662 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.663 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '1_month'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.663 | INFO     | __main__:fetch_last_available_location:254 - Stage 6 ('3_months'): Fetching GPS for VIN P53BXDCC6ECA00005 in window 2025-03-12 20:05:12+00:00 to 2025-06-10 20:05:12+00:00 with 7 retries.
2025-06-10 20:14:26.663 | INFO     | __main__:_fetch_raw_gps_data:142 - Fetching raw GPS data for VIN P53BXDCC6ECA00005 in range 1741809912000-1749585912000 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp
2025-06-10 20:14:26.663 | DEBUG    | __main__:_async_fetch_with_retry:92 - Attempting API call
2025-06-10 20:14:26.733 | INFO     | __main__:_async_fetch_with_retry:101 - API call successful
2025-06-10 20:14:26.733 | DEBUG    | __main__:fetch_last_available_location:286 - No GPS data found for VIN P53BXDCC6ECA00005 at stage '3_months'. Raw response: {'message': 'Request failed with status code 400', 'name': 'Error', 'stack': 'Error: Request failed with status code 400\n    at createError (/usr/src/app/node_modules/axios/lib/core/createError.js:16
2025-06-10 20:14:26.734 | WARNING  | __main__:fetch_last_available_location:292 - No GPS data found for VIN P53BXDCC6ECA00005 after all stages.
2025-06-10 20:14:26.734 | INFO     | __main__:main_ladh_test:334 - Fetched location for P53BXDCC6ECA00005: {'vin': 'P53BXDCC6ECA00005', 'data_found': False}
2025-06-10 20:14:26.735 | INFO     | __main__:main_ladh_test:336 - Standalone test for last_available_data_handler finished.