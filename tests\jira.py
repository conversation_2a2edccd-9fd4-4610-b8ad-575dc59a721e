from atlassian import Jira
import os
import dotenv
import json
import typer
import rich.console

class JiraIssuesFetcher:
    def __init__(self):
        dotenv.load_dotenv()
        self.jira = Jira(
            url=os.getenv('JIRA_URL'),
            username=os.getenv('JIRA_USERNAME'),
            password=os.getenv('JIRA_PASSWORD'),
            cloud=True
        )
        
    def fetch_issues(self, jql_query, output_file='jira/issues.json'):
        start = 0
        issues_list = []
        
        while True:
            all_issues = self.jira.jql(jql_query, limit=100, start=start)
            if all_issues['issues']:
                issues_list.extend(all_issues['issues'])
                start += 100
            else:
                break
                
        with open(output_file, 'w') as f:
            json.dump(issues_list, f, indent=4)
            
        return issues_list
    
    def get_xlsx(self, jql_query, output_file='jira/issues.xls'):
        # Create jira directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Get Excel bytes from Jira API
        excel_bytes = self.jira.excel(
            jql=jql_query,
            all_fields=True,
            limit=10
        )
        
        # Write bytes to file
        with open(output_file, 'wb') as f:
            f.write(excel_bytes)
        return output_file
    
    def get_csv(self, jql_query, output_file='jira/issues.csv'):
        # Create jira directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Get CSV bytes from Jira API
        csv_bytes = self.jira.csv(
            jql=jql_query,
            all_fields=True,
            limit=10
        )
        
        # Write bytes to file
        with open(output_file, 'wb') as f:
            f.write(csv_bytes)
        return output_file
        
    def get_all_priorities(self):
        return self.jira.get_all_priorities()
    
    def get_priority_by_id(self, priority_id):
        return self.jira.get_priority_by_id(priority_id)

# Example usage
if __name__ == '__main__':
    VETV_SOFTWARE_JQL = """issuetype = bug AND reporter in (712020:193dbe76-044b-4413-ba35-f70067bced71, 
        712020:2852f270-26f3-424e-b141-40773b312ef5, 712020:f9435b87-54ff-4638-bea8-0b74b90659b1, 
        712020:6937c798-8f57-4d4c-906e-791596f7ca38, 712020:2dcb0f73-0dd7-4c5c-b81f-d4d8db47b9f7, 
        712020:3514d123-50a5-4c49-bcfc-bc8cfb5cbb3e, 712020:227dbbb2-6590-4bb4-880a-3770739d7f85, 
        60238365be86a0006916a89e, 712020:7c78ac97-6ca9-4a03-9381-850085a86306) 
        AND created >= "2023/06/06" """
    
    app = typer.Typer()
    console = rich.console.Console()

    @app.command()
    def fetch_issues():
        """Fetch Jira issues and save to JSON"""
        fetcher = JiraIssuesFetcher()
        issues = fetcher.fetch_issues(VETV_SOFTWARE_JQL)
        console.print(f"[green]Successfully fetched {len(issues)} issues[/green]")

    @app.command()
    def get_xlsx():
        """Export Jira issues to Excel"""
        fetcher = JiraIssuesFetcher()
        output_file = fetcher.get_xlsx(VETV_SOFTWARE_JQL, output_file='jira/issues.xls')
        console.print(f"[green]Successfully exported issues to Excel: {output_file}[/green]")

    @app.command()
    def get_csv():
        """Export Jira issues to CSV"""
        fetcher = JiraIssuesFetcher()
        output_file = fetcher.get_csv(VETV_SOFTWARE_JQL, output_file='jira/issues.csv')
        console.print(f"[green]Successfully exported issues to CSV: {output_file}[/green]")

    @app.command()
    def get_all_priorities():
        """Get all Jira priorities"""
        fetcher = JiraIssuesFetcher()
        priorities = fetcher.get_all_priorities()
        console.print(priorities)

    @app.command()
    def get_priority_by_id(priority_id: str = typer.Argument(..., help="Priority ID to look up")):
        """Get a specific Jira priority by ID"""
        fetcher = JiraIssuesFetcher()
        priority = fetcher.get_priority_by_id(priority_id)
        console.print(priority)

    if __name__ == "__main__":
        app()

