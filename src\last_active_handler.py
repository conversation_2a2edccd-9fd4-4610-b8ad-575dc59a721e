# src/last_active_handler.py
import asyncio
import aiohttp
import os
import sys # Added for console logger
from loguru import logger
from typing import Optional, Dict, Any
from datetime import datetime

# This print statement helps debug if the script is even starting and what its CWD is.
print(f"Executing last_active_handler.py. Current CWD: {os.getcwd()}")
from src import config as app_config

# Configure logger for this module
LOG_DIR = os.path.join(app_config.LOGS_BASE_DIR, "last_active_handler")
os.makedirs(LOG_DIR, exist_ok=True)

logger.remove() # Remove default handler
logger.add(sys.stderr, level="DEBUG") # Add console sink for immediate feedback
logger.add(os.path.join(LOG_DIR, "info.log"), level="INFO", rotation="10 MB", retention="10 days")
logger.add(os.path.join(LOG_DIR, "debug.log"), level="DEBUG", rotation="10 MB", retention="10 days")

logger.info(f"Logger configured. CWD: {os.getcwd()}. Log directory: {os.path.abspath(LOG_DIR)}")

# Timeout configurations
INITIAL_TIMEOUT = aiohttp.ClientTimeout(total=17) # seconds
RETRY_TIMEOUT = aiohttp.ClientTimeout(total=20)   # seconds
RETRY_DELAY_SECONDS = 15

# Endpoint specific constant
LAST_ACTIVE_INFO_ENDPOINT = "/adxtelematics/v2/getVehicleInfoBMS"
DATA_TYPE_LAST_ACTIVE = "last_active_info"


async def _schedule_retry(
    session: aiohttp.ClientSession,
    url: str,
    vin: str,
    data_type: str,
    max_retries: int,
    retry_count: int
) -> Optional[Dict[str, Any]]:
    """Schedules a retry attempt with a delay."""
    logger.info(f"Scheduling retry #{retry_count}/{max_retries} for VIN {vin} ({data_type}) in {RETRY_DELAY_SECONDS}s")
    await asyncio.sleep(RETRY_DELAY_SECONDS)
    return await _async_fetch_with_retry(
        session, url, vin, data_type, max_retries, is_retry=True, retry_count=retry_count
    )

async def _async_fetch_with_retry(
    session: aiohttp.ClientSession,
    url: str,
    vin: str,
    data_type: str,
    max_retries: int,
    is_retry: bool = False,
    retry_count: int = 0
) -> Optional[Dict[str, Any]]:
    """
    Asynchronously fetches data from the given URL with a retry mechanism.
    Logs request and response details.
    """
    if retry_count >= max_retries:
        logger.error(f"Maximum retry attempts ({max_retries}) reached for VIN {vin} for {data_type} at {url}")
        # Log this attempt as failed due to max retries
        # Simplified logging compared to module.py's log_request to avoid file I/O here
        logger.bind(vin=vin, data_type=data_type, url=url, status_code=429, response="Max retries exceeded").error("API call failed (max retries)")
        return None

    timeout = RETRY_TIMEOUT if is_retry else INITIAL_TIMEOUT
    current_attempt = retry_count + 1
    log_context = {"vin": vin, "data_type": data_type, "url": url, "attempt": current_attempt, "max_retries": max_retries}

    try:
        logger.debug(f"Attempting API call", **log_context)
        async with session.get(url, timeout=timeout) as response:
            status_code = response.status
            response_text = await response.text() # Read text first for logging
            
            log_context_resp = {**log_context, "status_code": status_code}

            if status_code == 200:
                try:
                    data = await response.json(content_type=None) # Try to parse JSON, ignore content type
                    logger.bind(**log_context_resp, response_preview=str(data)[:200]).info("API call successful")
                    return data
                except Exception as e:
                    logger.bind(**log_context_resp, error=str(e), response_text=response_text).error("API call failed (JSON parsing error)")
                    # Even if JSON parsing fails, if it's a retryable HTTP error, it might be handled below
                    # For now, consider JSON parse error on 200 as a non-retryable failure for this data.
                    return None # Or specific error structure
            elif status_code == 204:
                logger.bind(**log_context_resp).info("API call successful (No Content)")
                return None # No content is a valid response, but no data
            else:
                logger.bind(**log_context_resp, response_text=response_text).warning("API call failed (HTTP error)")
                if status_code in [408, 429, 500, 502, 503, 504]: # Retry on these server/timeout errors
                    logger.info(f"Retrying due to status code {status_code}", **log_context)
                    return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt)
                return None # Non-retryable HTTP error for this specific fetch

    except asyncio.TimeoutError:
        logger.bind(**log_context).warning("API call failed (Timeout)")
        return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt)
    except aiohttp.ClientError as e: # Handles broader client errors like connection issues
        logger.bind(**log_context, error=str(e)).error("API call failed (ClientError)")
        # Retry on common connection errors
        if "Cannot connect" in str(e) or "Connection reset" in str(e) or "Connection refused" in str(e):
             logger.info(f"Retrying due to ClientError: {str(e)}", **log_context)
             return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt)
        return None
    except Exception as e:
        logger.bind(**log_context, error=str(e)).exception("API call failed (Unexpected Exception)")
        return None


async def fetch_last_active_details(
    session: aiohttp.ClientSession,
    vin: str,
    max_retries: int = 3 # Default max_retries if not specified by caller
) -> Dict[str, Any]:
    """
    Fetches basic vehicle information, primarily the last active timestamp.
    Adapts logic from module.py:async_fetch_last_active_info (lines 218-257).
    """
    base_url = app_config.OLA_TELEMATICS_BASE_URL
    url = f"{base_url}{LAST_ACTIVE_INFO_ENDPOINT}?vehicleId={vin}"
    
    logger.info(f"Fetching last active details for VIN: {vin} from {url} with max {max_retries} retries.")

    response_data = await _async_fetch_with_retry(session, url, vin, DATA_TYPE_LAST_ACTIVE, max_retries)

    if response_data and response_data.get('statusCode') == 200 and response_data.get('data'):
        vehicle_info_list = response_data['data']
        # Ensure data is a list and has content
        if isinstance(vehicle_info_list, list) and len(vehicle_info_list) > 0:
            vehicle_info = vehicle_info_list[0]
            if vehicle_info: # Check if vehicle_info (i.e. data[0]) is not empty
                return {
                    "timestamp": vehicle_info.get("timestamp"),
                    "pack_soc": vehicle_info.get("pack_soc"),
                    "scooter_state": vehicle_info.get("scooter_state"),
                    "time_charge_full_sc_1": vehicle_info.get("time_charge_full_sc_1"),
                    "status_code": response_data.get('statusCode'),
                    "message": response_data.get('message'),
                    "data_available": "Yes"
                }
        # Handle case where data array is empty or data[0] is empty or not a list
        logger.warning(f"Data array in response for VIN {vin} was empty, malformed, or not a list. Response: {str(response_data)[:500]}")
        return {
            "timestamp": None, "pack_soc": None, "scooter_state": None, "time_charge_full_sc_1": None,
            "status_code": response_data.get('statusCode'),
            "message": "Data array in response was empty or malformed.",
            "data_available": "No"
        }
    elif response_data: # Handle cases where API returned a non-200 statusCode or other issues but response_data is not None
        logger.warning(f"API call for VIN {vin} returned non-200 or no data. Status: {response_data.get('statusCode')}, Message: {response_data.get('message')}")
        return {
            "timestamp": None, "pack_soc": None, "scooter_state": None, "time_charge_full_sc_1": None,
            "status_code": response_data.get('statusCode', 500),
            "message": response_data.get('message', "Failed to fetch or parse data"),
            "data_available": "No"
        }
    
    # This case handles if _async_fetch_with_retry itself returned None (e.g., max retries exceeded, network error)
    logger.error(f"Failed to get any response for VIN {vin} after all retries or due to critical error.")
    return {
        "timestamp": None, "pack_soc": None, "scooter_state": None, "time_charge_full_sc_1": None,
        "status_code": "N/A", 
        "message": "Network/Retry error or no valid API response received.",
        "data_available": "No"
    }


if __name__ == "__main__":
    async def main():
        test_vin = "P53BKDCB3ECA00002" # Using a realistic VIN for standalone testing as per feedback
        
        logger.info(f"Starting standalone test for last_active_handler with VIN: {test_vin}")
        
        # It's good practice to use a timeout for the entire session in standalone tests
        # to prevent hanging indefinitely if something goes wrong with the network.
        conn = aiohttp.TCPConnector(limit_per_host=10) # Example connector settings
        async with aiohttp.ClientSession(connector=conn) as session:
            try:
                # Using a higher number of retries for standalone test to observe behavior
                details = await fetch_last_active_details(session, test_vin, max_retries=2)
                logger.info(f"Fetched details for {test_vin}: {details}")
            except Exception as e:
                logger.exception(f"An error occurred during standalone test for VIN {test_vin}: {e}")
            finally:
                logger.info("Standalone test finished.")

    # In Python 3.7+
    asyncio.run(main())