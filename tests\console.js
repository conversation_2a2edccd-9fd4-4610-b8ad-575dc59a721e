// async function to await "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/faultdata?vehicleId=P53BCDCA0DEA00028&fromTime=1733464967182&toTime=1733493767182"
async function getFaultData(vehicleId, fromTime, toTime) {
    const url = `https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/faultdata?vehicleId=${vehicleId}&fromTime=${fromTime}&toTime=${toTime}`;
    
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching fault data:', error);
        return null;
    }
}
// Example usage:
// const vehicleId = 'P53BCDCA0DEA00028';
// const fromTime = Date.now() - (30 * 60 * 1000); // 30 minutes ago
// const toTime = Date.now();
// getFaultData(vehicleId, fromTime, toTime).then(data => console.log(data));

// https://bam.nr-data.net/events/1/NRJS-8643ab09d227db48846?a=1120059328&sa=1&v=1215.1253ab8&t=Unnamed%20Transaction&rst=3275375&ck=1&ref=https://command-center-8080a.corp.olaelectric.com/scootersupportconsole/summaryconsolidated&ptid=c1c9d3fe-0001-b65c-34a6-01939c44adac

// https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/mcudata?vehicleId=P53BCDCA0DEA00028&fromTime=1733464967182&toTime=1733493767182&binSize=600
