import json
import ast

# The string as provided in the problem description
# This string represents a key-value pair, not a full JSON object yet.
original_string_fragment = "\"ecu_version\": \"{\"{\\\"hw_version\\\":\\\"9\\\",\\\"generic_version_details\\\":{},\\\"ecu_mfg_code\\\":\\\"1\\\",\\\"ecu_type\\\":\\\"SLOW_CHARGER\\\",\\\"firmware_version\\\":\\\"A\\\",\\\"ecu_serial_number\\\":\\\"89CF3DD5\\\",\\\"sw_bootloader_version\\\":\\\"8\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"66.12.15.B\\\",\\\"hw_version\\\":\\\"1 0\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"MASTER_BMS\\\",\\\"sw_asw_version\\\":\\\"2025050202\\\",\\\"sw_bootloader_version\\\":\\\"64.03.01\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"16.00.15\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"HU\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"17.00.01\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"BCM\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"15.00.46\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"HMI\\\"}\",\"{\\\"sw_bsw_version\\\":\\\"***********\\\",\\\"hw_version\\\":\\\"25118001\\\",\\\"generic_version_details\\\":{},\\\"ecu_type\\\":\\\"MCU\\\",\\\"sw_bootloader_version\\\":\\\"01.00.00\\\"}\"}\""

# Step 1: Wrap the fragment in curly braces to make it a valid JSON string
complete_json_string = "{" + original_string_fragment + "}"

# Step 2: Parse this outer JSON structure
try:
    outer_data = json.loads(complete_json_string)
except json.JSONDecodeError as e:
    print(f"Error decoding the outer JSON string: {e}")
    print(f"String being parsed: {complete_json_string[:200]}...") # Print first 200 chars
    exit()

# Step 3: Extract the value associated with "ecu_version".
# This value is a string that looks like a Python set literal.
ecu_version_string_value = outer_data.get("ecu_version")

if ecu_version_string_value is None:
    print("Error: 'ecu_version' key not found in the parsed outer JSON.")
    exit()

# Step 4: Use ast.literal_eval to parse this string representation of a set
# into an actual Python set of strings. Each string in this set is an
# escaped JSON object string.
try:
    set_of_json_strings = ast.literal_eval(ecu_version_string_value)
except (ValueError, SyntaxError) as e:
    print(f"Error evaluating the ecu_version string value with ast.literal_eval: {e}")
    print(f"String being evaluated: {ecu_version_string_value[:200]}...")
    exit()


# Step 5: Iterate through the set of JSON strings and parse each one.
# Store the results in a list.
parsed_ecu_list = []
for json_string_item in set_of_json_strings:
    try:
        # Each json_string_item is like "{\"key\":\"value\"}"
        # json.loads will handle the escaped quotes correctly.
        ecu_dict = json.loads(json_string_item)
        parsed_ecu_list.append(ecu_dict)
    except json.JSONDecodeError as e:
        print(f"Error decoding an inner JSON string: {e}")
        print(f"Inner JSON string being parsed: {json_string_item}")
        # Optionally, decide whether to continue or stop on error
        # continue

# Verification:
print(f"Successfully parsed. Number of ECU entries: {len(parsed_ecu_list)}")
print("Type of the final result:", type(parsed_ecu_list))
if parsed_ecu_list:
    print("Type of an element in the list:", type(parsed_ecu_list[0]))
    print("\nFirst ECU entry (example):")
    # Pretty print the first dictionary
    import pprint
    pprint.pprint(parsed_ecu_list[0])

    print("\nAll parsed ECU entries:")
    for i, entry in enumerate(parsed_ecu_list):
        print(f"--- Entry {i+1} ---")
        pprint.pprint(entry)
else:
    print("No ECU entries were parsed.")