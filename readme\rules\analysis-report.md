### Reporting
When you want to make sense of the current codebase:
- Generate a current_status_<ddMMMyyHHMM>.md file within `readme/reports/` (e.g., `current_status_05May251250.md`)
- **Timestamp Format:** `dd` (day), `MMM` (3-letter month), `yy` (year), `HH` (hour 24h), `MM` (minute). **Do not** add a 'd' prefix to the day.
- Removal of redundant code will be mentioned within removal_<ddMMMyyHHMM>.md file within `readme/reports/` (e.g., `removal_05May251250.md`)
- Addition of new code will be mentioned within addition_<ddMMMyyHHMM>.md file within `readme/reports/` (e.g., `addition_05May251250.md`)
- Optimization of existing code will be mentioned within optimization_<ddMMMyyHHMM>.md file within `readme/reports/` (e.g., `optimization_05May251250.md`)
