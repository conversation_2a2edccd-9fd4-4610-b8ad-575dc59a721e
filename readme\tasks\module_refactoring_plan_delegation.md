# Comprehensive Refactoring Plan for `module.py`

**Objective:** To improve code organization, readability, testability, and maintainability of `module.py` by separating distinct responsibilities into different Python modules. These new modules will reside in a `src/` directory. The main `module.py` (as an orchestrator) and `runner.py` will remain in the project root. Ruff will be used for linting and formatting throughout the process.

**Target File Structure:**
```
telematics_command_center/
|-- module.py
|-- runner.py
|-- src/
|   |-- __init__.py
|   |-- config.py
|   |-- utils.py
|   |-- data_persistence.py
|   |-- api_clients.py
|   |-- google_sheets_handler.py
|-- (other existing files and directories)
```

---

**Refactoring Phases and Detailed Steps:**

**Phase 0: Initial Setup**
1.  **Create `src/` Directory:**
    *   Action: In the project root, create a new directory named `src`.
2.  **Create `src/__init__.py`:**
    *   Action: Inside the `src/` directory, create an empty file named `__init__.py`.
    *   Content: Add a comment like `# src package initializer`. This makes the `src` directory a Python package.
3.  **Ruff Check (Baseline):**
    *   Action: Run Ruff on the current `module.py` to establish a clean baseline.
    *   Commands: `ruff check . --fix` and `ruff format .` (or equivalent for your setup).

---

**Phase 1: Configuration Module (`src/config.py`)**
1.  **Create `src/config.py`**.
2.  **Identify and Move Constants from `module.py` to `src/config.py`:**
    *   **`STAGES_CONFIG`**: The list of stage dictionaries for data fetching.
    *   **`DEFAULT_FEATURE_FLAGS`**: The default `feature_flags` dictionary (rename from `feature_flags` in `module.py`).
    *   **`GOOGLE_SCOPES`**: The `SCOPES` list for Google Auth (rename from `SCOPES` in `module.py`).
    *   **`MASTER_SHEET_ID`**: The master Google Sheet ID.
    *   **`MASTER_VIN_LIST_SHEET_NAME`**: The sheet name for the VIN list.
    *   **`PICKLE_FILE_NAME`**: The name for the pickle file (e.g., `'request_results.pickle'`).
    *   **Logging Directory Constants**:
        *   `LOGS_BASE_DIR = 'logs'`
        *   `MODULE_LOG_SUBDIR = 'module'`
    *   **API Base URLs and Endpoint Paths**:
        *   `OLA_TELEMATICS_BASE_URL = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics"`
        *   `NOMINATIM_BASE_URL = "https://nominatim.openstreetmap.org/reverse"`
        *   `NETWORK_TIMESTAMP_ENDPOINT = "/connectivityreport/timerange/networktimestamp"`
        *   `CONNECTIVITY_DATA_ENDPOINT = "/adxtelematics/v2/getNetworkData"`
        *   `GPS_TIMESTAMP_ENDPOINT = "/connectivityreport/timerange/gpstimestamp"`
        *   `RSRP_ENDPOINT = "/connectivityreport/percentile/rsrp"`
        *   `HORIZONTAL_ACCURACY_ENDPOINT = "/connectivityreport/percentile/horizontalaccuracy"`
        *   `VERSION_INFO_ENDPOINT = "/vehicle/versioninfo"`
3.  **Update `module.py` to Use `src.config`:**
    *   Add `from src import config` at the top of `module.py`.
    *   Remove the original definitions of these constants from `NetworkTimestampChecker` class and `__init__`.
    *   Replace all usages of these constants with `config.CONSTANT_NAME`.
        *   Example: `NetworkTimestampChecker.STAGES_CONFIG` (class variable) becomes `config.STAGES_CONFIG`.
        *   Example: `self.feature_flags = {...}` in `__init__` becomes `self.feature_flags = config.DEFAULT_FEATURE_FLAGS.copy()`.
        *   Example: `self.pickle_file` usage becomes `config.PICKLE_FILE_NAME`.
        *   Update the global `logs_dir_path` (or `logs_dir`) definition at the top of `module.py` to use `os.path.join(os.getcwd(), config.LOGS_BASE_DIR, config.MODULE_LOG_SUBDIR)`.
        *   Update all `async_fetch_<datatype>` methods and `get_location_name` to construct their full request URLs using `config.OLA_TELEMATICS_BASE_URL`, `config.NOMINATIM_BASE_URL`, and the specific endpoint path constants from `config`.
4.  **Ruff Check:** Run `ruff check . --fix` and `ruff format .` on `src/config.py` and `module.py`.

---

**Phase 2: Utilities Module (`src/utils.py`)**
1.  **Create `src/utils.py`**.
2.  **Move `convert_timestamp()`:**
    *   Action: Move the `convert_timestamp` method from `NetworkTimestampChecker` in `module.py` to `src/utils.py` as a standalone function.
3.  **Update `module.py`:**
    *   Add `from src import utils` (or `from src.utils import convert_timestamp`).
    *   Update all calls from `self.convert_timestamp(...)` to `utils.convert_timestamp(...)` (or `convert_timestamp(...)` if imported directly).
4.  **Ruff Check:** Run Ruff on `src/utils.py` and `module.py`.

---

**Phase 3: Data Persistence Module (`src/data_persistence.py`)**
1.  **Create `src/data_persistence.py`**.
2.  **Define `PersistenceHandler` Class (Recommended) or Standalone Functions:**
    *   This module will handle pickle operations and request logging.
    *   **Move Logic for:**
        *   `load_pickle_data()`
        *   `save_pickle_data()`
        *   `update_pickle_data()`
        *   `log_request()`
    *   If creating a `PersistenceHandler` class:
        *   Its `__init__` could accept `pickle_file_name`, `request_log_file_path`, and a `threading.Lock` instance.
        *   The above functions become methods of this class.
3.  **Update `module.py` (`NetworkTimestampChecker`):**
    *   Add `from src.data_persistence import PersistenceHandler` (or import functions if not using a class).
    *   In `NetworkTimestampChecker.__init__`:
        *   Remove `self.lock = threading.Lock()`. The lock will be managed by or passed to `PersistenceHandler`.
        *   Instantiate the handler: `self.persistence_handler = PersistenceHandler(pickle_file_name=config.PICKLE_FILE_NAME, request_log_file=self.request_log_file, lock=threading.Lock())`. (Note: `self.request_log_file` path construction might also move or be passed).
    *   Replace calls like `self.save_pickle_data(...)` with `self.persistence_handler.save_pickle_data(...)`.
    *   The `self.lock` used in `log_request` and pickle methods will now be internal to `PersistenceHandler` or passed to its methods.
4.  **Ruff Check:** Run Ruff on `src/data_persistence.py` and `module.py`.

---

**Phase 4: API Clients Module (`src/api_clients.py`)**
1.  **Create `src/api_clients.py`**.
2.  **Define `ApiClient` Class (Recommended):**
    *   The `__init__` should accept:
        *   `fetch_semaphore` (the `asyncio.Semaphore` instance).
        *   `timeout` and `retry_timeout` (the `aiohttp.ClientTimeout` instances).
        *   Relevant API URL configurations from `src.config`.
    *   **Move Methods from `NetworkTimestampChecker` to `ApiClient`:**
        *   All `async_fetch_<datatype>()` methods (e.g., `async_fetch_network_timestamp`).
        *   `get_location_name()`.
        *   `async_fetch_with_retry()`.
        *   `schedule_retry()`.
    *   These methods within `ApiClient` will use the API URL constants and timeout/semaphore configurations passed during its initialization.
3.  **Update `module.py` (`NetworkTimestampChecker`):**
    *   Add `from src.api_clients import ApiClient`.
    *   In `NetworkTimestampChecker.__init__`:
        *   Remove `self.timeout`, `self.retry_timeout`. These will be managed by `ApiClient`.
        *   Instantiate `ApiClient`: `self.api_client = ApiClient(fetch_semaphore=self.fetch_semaphore, timeout_config=self.timeout, retry_timeout_config=self.retry_timeout, api_config=config)`. (Note: `self.timeout` and `self.retry_timeout` are existing `aiohttp.ClientTimeout` objects that can be passed).
    *   In `async_batch_fetch_all_v2`:
        *   Calls like `self.async_fetch_network_timestamp(session, ...)` will become `self.api_client.async_fetch_network_timestamp(session, ...)`. The `aiohttp.ClientSession` can still be created in `async_batch_fetch_all_v2` and passed to the `ApiClient` methods.
4.  **Ruff Check:** Run Ruff on `src/api_clients.py` and `module.py`.

---

**Phase 5: Google Sheets Handler Module (`src/google_sheets_handler.py`)**
1.  **Create `src/google_sheets_handler.py`**.
2.  **Define `GoogleSheetsHandler` Class (Recommended):**
    *   The `__init__` could accept Google auth related configurations (e.g., `config.GOOGLE_SCOPES`, `config.MASTER_SHEET_ID`, `config.MASTER_VIN_LIST_SHEET_NAME`, paths to auth files like `'auth/token.pickle'` and `'auth/auth.json'`).
    *   **Move Methods from `NetworkTimestampChecker` to `GoogleSheetsHandler`:**
        *   `authenticate_google_drive()`
        *   `get_google_sheet()`
        *   `update_or_create_result_google_sheet()`
        *   All `format_<datatype>_data()` methods.
        *   `get_default_row()`
3.  **Update `module.py` (`NetworkTimestampChecker`):**
    *   Add `from src.google_sheets_handler import GoogleSheetsHandler`.
    *   In `NetworkTimestampChecker.__init__`:
        *   Instantiate `GoogleSheetsHandler`: `self.sheets_handler = GoogleSheetsHandler(config=config)`.
    *   Replace calls like `self.update_or_create_result_google_sheet(...)` with `self.sheets_handler.update_or_create_result_google_sheet(...)`.
    *   The `get_vin_numbers()` method in `NetworkTimestampChecker` will call `self.sheets_handler.authenticate_google_drive()` and then `self.sheets_handler.get_google_sheet(...)`.
4.  **Ruff Check:** Run Ruff on `src/google_sheets_handler.py` and `module.py`.

---

**Phase 6: Final Review and Cleanup of `module.py`**
1.  **Review `NetworkTimestampChecker` in `module.py`:**
    *   Confirm it primarily serves as an orchestrator, delegating specific tasks to instances of the new handler/client classes.
    *   Its `__init__` should correctly set up these handler/client instances, passing necessary configurations and shared objects (like the semaphore).
    *   `run_async()` and `async_batch_fetch_all_v2()` will contain the core staging and batching logic but will make calls to methods of the handler/client objects.
2.  **Verify Imports:**
    *   Check for any unused imports in all modified files.
    *   Ensure all necessary components are correctly imported (e.g., `from src.config import ...`, `from src.utils import ...`, etc.).
3.  **Global Variables in `module.py`:**
    *   Ensure the logging setup at the top of `module.py` (using `logs_dir_path`) is correct and uses constants from `src.config`.
4.  **Ruff Check (Final Pass):**
    *   Run `ruff check . --fix` and `ruff format .` on `module.py` and all files within the `src/` directory for a final cleanup.

---

This comprehensive plan should guide the refactoring process effectively.