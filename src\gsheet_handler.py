# src/gsheet_handler.py
import os
import pickle
from typing import List, Any, Optional
from loguru import logger
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from src import config as app_config

def authenticate_google_services() -> Any:
    """
    Authenticates and returns Google Sheets service object.
    Adapts logic from module.py:636-655.
    """
    creds = None
    token_file = 'token.pickle'
    credentials_file = 'credentials.json'

    # The file token.pickle stores the user's access and refresh tokens.
    if os.path.exists(token_file):
        with open(token_file, 'rb') as token:
            creds = pickle.load(token)

    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
                logger.info("Google credentials refreshed successfully")
            except Exception as e:
                logger.error(f"Failed to refresh Google credentials: {e}")
                creds = None

        if not creds:
            if not os.path.exists(credentials_file):
                logger.error(f"Google credentials file '{credentials_file}' not found")
                raise FileNotFoundError(f"Google credentials file '{credentials_file}' not found")

            flow = InstalledAppFlow.from_client_secrets_file(
                credentials_file, app_config.GOOGLE_SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New Google credentials obtained")

        # Save the credentials for the next run
        with open(token_file, 'wb') as token:
            pickle.dump(creds, token)
            logger.info("Google credentials saved to token.pickle")

    try:
        service = build('sheets', 'v4', credentials=creds)
        logger.info("Google Sheets service authenticated successfully")
        return service
    except Exception as e:
        logger.error(f"Failed to build Google Sheets service: {e}")
        raise

def create_or_get_sheet(service: Any, spreadsheet_id: str, sheet_name: str) -> str:
    """
    Creates a new sheet with the given name if it doesn't exist, or returns the existing sheet name.
    Returns the sheet name (which might be modified if there was a conflict).
    """
    try:
        # Get existing sheets
        spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]

        if sheet_name in existing_sheets:
            logger.info(f"Sheet '{sheet_name}' already exists")
            return sheet_name

        # Create new sheet
        request_body = {
            'requests': [{
                'addSheet': {
                    'properties': {
                        'title': sheet_name
                    }
                }
            }]
        }

        response = service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body=request_body
        ).execute()

        logger.info(f"Created new sheet '{sheet_name}'")
        return sheet_name

    except HttpError as e:
        logger.error(f"Failed to create/get sheet '{sheet_name}': {e}")
        raise

def clear_sheet_data(service: Any, spreadsheet_id: str, sheet_name: str) -> None:
    """
    Clears all data from the specified sheet.
    """
    try:
        range_name = f"{sheet_name}!A:Z"  # Clear columns A through Z
        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_id,
            range=range_name
        ).execute()
        logger.info(f"Cleared data from sheet '{sheet_name}'")
    except HttpError as e:
        logger.error(f"Failed to clear sheet '{sheet_name}': {e}")
        raise

def update_consolidated_vehicle_report(
    service: Any,
    spreadsheet_id: str,
    sheet_name: str,
    report_data: List[List],
    clear_existing: bool = True
) -> None:
    """
    Takes the list of formatted rows (from format_handler.py).
    Clears the existing sheet (or updates rows intelligently).
    Writes the new header and data to the specified single sheet.
    This function embodies the "tracking limited to 1 sheet" requirement.
    """
    try:
        # Ensure the sheet exists
        actual_sheet_name = create_or_get_sheet(service, spreadsheet_id, sheet_name)

        # Clear existing data if requested
        if clear_existing:
            clear_sheet_data(service, spreadsheet_id, actual_sheet_name)

        # Prepare data with headers
        if not report_data:
            logger.warning("No report data provided to write to sheet")
            return

        # Write data to sheet
        range_name = f"{actual_sheet_name}!A1"
        value_input_option = 'RAW'  # or 'USER_ENTERED' if you want Google Sheets to interpret the data

        body = {
            'values': report_data
        }

        result = service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_id,
            range=range_name,
            valueInputOption=value_input_option,
            body=body
        ).execute()

        updated_cells = result.get('updatedCells', 0)
        logger.info(f"Successfully updated {updated_cells} cells in sheet '{actual_sheet_name}'")
        logger.info(f"Written {len(report_data)} rows to sheet '{actual_sheet_name}'")

    except HttpError as e:
        logger.error(f"Failed to update consolidated vehicle report: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating sheet: {e}")
        raise

if __name__ == "__main__":
    # Standalone execution for testing
    print("Testing gsheet_handler.py")

    try:
        # Test authentication (this will require credentials.json to be present)
        print("\n--- Testing Google Sheets Authentication ---")
        service = authenticate_google_services()
        print("Authentication successful!")

        # Test with sample data (using a test spreadsheet ID)
        # Note: Replace with actual spreadsheet ID for real testing
        test_spreadsheet_id = "YOUR_TEST_SPREADSHEET_ID_HERE"
        test_sheet_name = "Network Tracking Data Test"

        # Sample report data with headers
        sample_headers = ["VIN", "Location", "Software Version", "BMS Version", "BCM Version", "MCU Version", "Last Connected"]
        sample_data = [
            ["P53AWDCC2CEA00079", "Bangalore, Karnataka", "5.0.1.gen3.0514", "66 13 05 L", "88 09 62", "52 02 83 01", "2025-06-09 06:59:24"]
        ]

        report_data = [sample_headers] + sample_data

        print(f"\n--- Testing Sheet Update (would update sheet '{test_sheet_name}') ---")
        print(f"Sample data to write: {len(report_data)} rows")
        print(f"Headers: {sample_headers}")
        print(f"Sample row: {sample_data[0]}")

        # Uncomment the following lines to actually test with a real spreadsheet
        # update_consolidated_vehicle_report(service, test_spreadsheet_id, test_sheet_name, report_data)
        # print("Sheet update successful!")

        print("\nNote: To test actual sheet updates, provide a valid spreadsheet ID and uncomment the update call.")

    except FileNotFoundError as e:
        print(f"Credentials file not found: {e}")
        print("Please ensure 'credentials.json' is present in the working directory.")
    except Exception as e:
        print(f"Error during testing: {e}")

    print("\ngsheet_handler.py testing completed.")