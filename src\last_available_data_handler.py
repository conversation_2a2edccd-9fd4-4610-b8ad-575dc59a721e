# src/last_available_data_handler.py
import asyncio
import json
import aiohttp
import os
import sys
from loguru import logger
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from src import config as app_config

# This print statement helps debug if the script is even starting and what its CWD is.
print(f"Executing last_available_data_handler.py. Current CWD: {os.getcwd()}")


# Configure logger for this module
LOG_DIR = os.path.join(app_config.LOGS_BASE_DIR, "last_available_data_handler")
os.makedirs(LOG_DIR, exist_ok=True)

logger.remove() # Remove default handler
logger.add(sys.stderr, level="DEBUG") # Add console sink
logger.add(os.path.join(LOG_DIR, "info.log"), level="INFO", rotation="10 MB", retention="10 days")
logger.add(os.path.join(LOG_DIR, "debug.log"), level="DEBUG", rotation="10 MB", retention="10 days")

logger.info(f"Logger configured. CWD: {os.getcwd()}. Log directory: {os.path.abspath(LOG_DIR)}")

# Timeout configurations (can be centralized later)
INITIAL_TIMEOUT = aiohttp.ClientTimeout(total=17)
RETRY_TIMEOUT = aiohttp.ClientTimeout(total=20)
RETRY_DELAY_SECONDS = 15

# --- Helper: Timestamp Conversion ---
def string_timestamp_to_ms_epoch(timestamp_str: str, assume_utc: bool = True) -> Optional[int]:
    """
    Converts a string timestamp (e.g., "2025-06-08 22:58:20") to milliseconds epoch.
    Assumes UTC if timezone info is not present in the string.
    """
    if not timestamp_str:
        return None
    try:
        # Attempt to parse common formats, including those with fractional seconds
        dt_obj = None
        formats_to_try = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M:%S.%f", # Handles milliseconds if present
            "%Y-%m-%dT%H:%M:%SZ",   # ISO format UTC
            "%Y-%m-%dT%H:%M:%S.%fZ" # ISO format UTC with milliseconds
        ]
        for fmt in formats_to_try:
            try:
                dt_obj = datetime.strptime(timestamp_str, fmt)
                break
            except ValueError:
                continue
        
        if dt_obj is None:
            logger.error(f"Could not parse timestamp string: {timestamp_str} with known formats.")
            return None

        if assume_utc and dt_obj.tzinfo is None:
            dt_obj = dt_obj.replace(tzinfo=timezone.utc)
        return int(dt_obj.timestamp() * 1000)
    except Exception as e:
        logger.error(f"Error converting string timestamp '{timestamp_str}' to ms epoch: {e}")
        return None

# --- API Fetching Helpers (adapted from last_active_handler.py) ---
async def _schedule_retry(
    session: aiohttp.ClientSession, url: str, vin: str, data_type: str,
    max_retries: int, retry_count: int, params: Optional[Dict] = None
) -> Optional[Dict[str, Any]]:
    logger.info(f"Scheduling retry #{retry_count}/{max_retries} for VIN {vin} ({data_type}) in {RETRY_DELAY_SECONDS}s for URL {url}")
    await asyncio.sleep(RETRY_DELAY_SECONDS)
    return await _async_fetch_with_retry(
        session, url, vin, data_type, max_retries, is_retry=True, retry_count=retry_count, params=params
    )

async def _async_fetch_with_retry(
    session: aiohttp.ClientSession, url: str, vin: str, data_type: str,
    max_retries: int, is_retry: bool = False, retry_count: int = 0,
    params: Optional[Dict] = None, headers: Optional[Dict] = None
) -> Optional[Dict[str, Any]]:
    if retry_count >= max_retries:
        logger.error(f"Max retries ({max_retries}) for VIN {vin}, {data_type} at {url}")
        logger.bind(vin=vin, data_type=data_type, url=url, params=params, status_code=429).error("API call failed (max retries)")
        return None

    timeout = RETRY_TIMEOUT if is_retry else INITIAL_TIMEOUT
    current_attempt = retry_count + 1
    log_ctx = {"vin": vin, "data_type": data_type, "url": url, "params": params, "attempt": current_attempt, "max_retries": max_retries}

    try:
        logger.debug(f"Attempting API call", **log_ctx)
        async with session.get(url, params=params, timeout=timeout, headers=headers) as response:
            status_code = response.status
            response_text = await response.text()
            log_ctx_resp = {**log_ctx, "status_code": status_code}

            if status_code == 200:
                try:
                    data = await response.json(content_type=None)
                    logger.bind(**log_ctx_resp, response_preview=str(data)[:200]).info("API call successful")
                    return data
                except Exception as e:
                    logger.bind(**log_ctx_resp, error=str(e), response_text=response_text).error("JSON parsing error")
                    return None
            elif status_code == 204:
                logger.bind(**log_ctx_resp).info("API call successful (No Content)")
                return {"data": [], "message": "No Content", "statusCode": 204} # Return a structure indicating no data
            else:
                logger.bind(**log_ctx_resp, response_text=response_text).warning(f"API call failed (HTTP error {status_code})")
                if status_code == 400:
                    try:
                        parsed_error_body = json.loads(response_text)
                        logger.info(f"HTTP 400 error for {vin} on {data_type}, returning parsed JSON body: {str(parsed_error_body)[:200]}")
                        return parsed_error_body # This makes the log at line 286 consistent
                    except json.JSONDecodeError:
                        logger.error(f"HTTP 400 error for {vin} on {data_type}, but response body was not JSON: {response_text[:200]}")
                        # Fall through to return None or a generic error structure below
                
                # For other non-200/204 errors
                if status_code in [408, 429, 500, 502, 503, 504]: # Retryable errors
                    return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt, params=params, headers=headers)
                
                # For non-retryable, non-400 errors, or 400 with non-JSON body
                logger.warning(f"Returning None for unhandled HTTP {status_code} for {vin} on {data_type}")
                return None
    except asyncio.TimeoutError:
        logger.bind(**log_ctx).warning("API call failed (Timeout)")
        return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt, params=params, headers=headers)
    except aiohttp.ClientError as e:
        logger.bind(**log_ctx, error=str(e)).error("API call failed (ClientError)")
        if "Cannot connect" in str(e) or "Connection reset" in str(e) or "Connection refused" in str(e):
            return await _schedule_retry(session, url, vin, data_type, max_retries, current_attempt, params=params, headers=headers)
        return None
    except Exception as e:
        logger.bind(**log_ctx, error=str(e)).exception("API call failed (Unexpected Exception)")
        return None

# --- Specific Data Fetchers ---
async def _fetch_raw_version_info(
    session: aiohttp.ClientSession, vin: str, max_retries: int
) -> Optional[Dict[str, Any]]:
    """Fetches raw version info (point-in-time, no time range)."""
    url = f"{app_config.OLA_TELEMATICS_BASE_URL}{app_config.VERSION_INFO_ENDPOINT}"
    params = {"vin": vin}
    logger.info(f"Fetching raw version info for VIN {vin} from {url}")
    return await _async_fetch_with_retry(session, url, vin, "version_info_raw", max_retries, params=params)

async def _fetch_raw_gps_data(
    session: aiohttp.ClientSession, vin: str, from_time_ms: int, to_time_ms: int, max_retries: int
) -> Optional[Dict[str, Any]]:
    """Fetches raw GPS data for a given time range."""
    url = f"{app_config.OLA_TELEMATICS_BASE_URL}{app_config.GPS_TIMESTAMP_ENDPOINT}"
    params = {"vehicleId": vin, "fromTime": from_time_ms, "toTime": to_time_ms}

    # Log complete URL with query parameters for debugging
    complete_url = f"{url}?vehicleId={vin}&fromTime={from_time_ms}&toTime={to_time_ms}"
    logger.info(f"Fetching raw GPS data for VIN {vin} in range {from_time_ms}-{to_time_ms} from {url}")
    logger.info(f"Complete GPS URL for debugging: {complete_url}")

    return await _async_fetch_with_retry(session, url, vin, "gps_data_raw", max_retries, params=params)

async def _reverse_geocode_location(
    session: aiohttp.ClientSession, latitude: str, longitude: str, max_retries: int
) -> Optional[str]:
    """Performs reverse geocoding using Nominatim."""
    if not latitude or not longitude:
        return None
    url = f"{app_config.NOMINATIM_BASE_URL}"
    params = {"lat": latitude, "lon": longitude, "format": "json"}
    headers = {'User-Agent': 'OlaNetworkTimestampChecker/1.1'} # Updated user agent slightly
    
    logger.info(f"Reverse geocoding for lat: {latitude}, lon: {longitude}")
    response_data = await _async_fetch_with_retry(
        session, url, f"latlon-{latitude}-{longitude}", "reverse_geocode", max_retries, params=params, headers=headers
    )

    if response_data and response_data.get('address'):
        address = response_data.get('address', {})
        zone = address.get('suburb', '') or address.get('neighbourhood', '')
        city = address.get('city', '') or address.get('town', '') or address.get('village', '')
        state = address.get('state', '')
        location_parts = [part for part in [zone, city, state] if part]
        return ', '.join(location_parts) if location_parts else ''
    logger.warning(f"Failed to reverse geocode or address not found for lat: {latitude}, lon: {longitude}. Response: {str(response_data)[:200]}")
    return None

# --- Main Public Functions for Phase 2 ---
async def fetch_last_available_versions(
    session: aiohttp.ClientSession,
    vin: str,
    # reference_timestamp_ms: int, # Version info is point-in-time, not typically ranged for "last available" in this context
    # stages_config: List[Dict[str, Any]], # Not directly used if version is point-in-time
    max_retries_overall: int = 3 # Max retries for this specific fetch attempt
) -> Optional[Dict[str, Any]]:
    """
    Fetches the last available version information for a VIN.
    Version info is typically point-in-time, so staged fallback based on time might not apply directly
    unless the API itself supports historical version snapshots (which is not implied by module.py:211-217).
    """
    logger.info(f"Attempting to fetch last available versions for VIN {vin}")
    
    # For version info, it's usually a single call to get the current/last known state.
    # The concept of "stages" might not apply in the same way as time-ranged data.
    # We'll use max_retries_overall for this single attempt.
    version_data_raw = await _fetch_raw_version_info(session, vin, max_retries_overall)

    if version_data_raw and version_data_raw.get('data'):
        # Assuming 'data' contains a list and we take the first element, as per module.py format_version_info
        data_list = version_data_raw['data']
        if isinstance(data_list, list) and len(data_list) > 0:
            actual_data = data_list[0]
            # We need to return enough info for format_handler, including the raw ecu_version string
            # and the timestamp associated with this version data if available from API.
            return {
                "vin": vin,
                "vehicle_software_version": actual_data.get("vehicle_software_version"),
                "move_os_version": actual_data.get("move_os_version"),
                "platform_config_version": actual_data.get("platform_config_version"),
                "app_config_version": actual_data.get("app_config_version"),
                "services_config_version": actual_data.get("services_config_version"),
                "ecu_config_version": actual_data.get("ecu_config_version"),
                "bcm_ble_version": actual_data.get("bcm_ble_version"),
                "hmi_version": actual_data.get("hmi_version"),
                "otam_version": actual_data.get("otam_version"),
                "firmware_version": actual_data.get("firmware_version"),
                "hardware_version": actual_data.get("hardware_version"),
                "scooter_variant": actual_data.get("scooter_variant"),
                "battery_id": actual_data.get("battery_id"),
                "scooter_id": actual_data.get("scooter_id"),
                "som_id": actual_data.get("som_id"),
                "ecu_version": actual_data.get("ecu_version"), # Crucial for BMS/MCU/BCM parsing
                "timestamp_of_data": actual_data.get("timestamp"), # Timestamp from the version data itself
                "api_status_code": version_data_raw.get("statusCode"),
                "api_message": version_data_raw.get("message"),
                "data_found": True
            }
    logger.warning(f"No version data found for VIN {vin} after retries. Raw response: {str(version_data_raw)[:200]}")
    return {"vin": vin, "data_found": False, "api_status_code": version_data_raw.get("statusCode") if version_data_raw else "N/A"}


async def fetch_last_available_location(
    session: aiohttp.ClientSession,
    vin: str,
    reference_timestamp_ms: int, # Milliseconds epoch of last active time
    stages_config: List[Dict[str, Any]],
    # max_retries_per_stage_fn: callable # Replaced by direct passing of retries
) -> Optional[Dict[str, Any]]:
    """
    Fetches the last available GPS location for a VIN, using a staged approach
    around the reference_timestamp_ms. Then reverse geocodes it.
    """
    logger.info(f"Attempting to fetch last available location for VIN {vin} around {reference_timestamp_ms}")
    current_to_time = reference_timestamp_ms # Start by looking at the exact last active time

    for stage_idx, stage in enumerate(stages_config):
        stage_name = stage["name"]
        duration_spec = stage["duration_spec"]
        retries_for_stage = stage["retries"]
        
        from_time_offset_ms = 0
        if "days" in duration_spec:
            from_time_offset_ms = duration_spec["days"] * 24 * 60 * 60 * 1000
        elif "hours" in duration_spec:
            from_time_offset_ms = duration_spec["hours"] * 60 * 60 * 1000
        
        # Query window for this stage: [current_to_time - offset, current_to_time]
        # More accurately, for "last available", we should search backwards from reference_timestamp_ms
        stage_from_time_ms = current_to_time - from_time_offset_ms
        stage_to_time_ms = current_to_time # The 'to' time is the reference point we are looking back from

        logger.info(f"Stage {stage_idx+1} ('{stage_name}'): Fetching GPS for VIN {vin} in window "
                    f"{datetime.fromtimestamp(stage_from_time_ms/1000, tz=timezone.utc)} to "
                    f"{datetime.fromtimestamp(stage_to_time_ms/1000, tz=timezone.utc)} with {retries_for_stage} retries.")

        # Ensure proper awaiting of GPS response
        logger.debug(f"About to await GPS data fetch for VIN {vin} at stage '{stage_name}'")
        gps_data_raw = await _fetch_raw_gps_data(session, vin, stage_from_time_ms, stage_to_time_ms, retries_for_stage)
        logger.debug(f"GPS data fetch completed for VIN {vin} at stage '{stage_name}'. Response type: {type(gps_data_raw)}")

        if gps_data_raw and gps_data_raw.get('data'):
            data_list = gps_data_raw['data']
            if isinstance(data_list, list) and len(data_list) > 0:
                # Assuming the API returns data sorted by time, take the latest one (first in list if descending, last if ascending)
                # For simplicity, let's take the first record if multiple are returned in the window.
                # A more robust approach might sort them by timestamp if not guaranteed by API.
                latest_gps_point = data_list[0] # Assuming latest is first, or just take any valid point
                lat = latest_gps_point.get('latitude')
                lon = latest_gps_point.get('longitude')
                gps_timestamp_str = latest_gps_point.get('timestamp') # This is the timestamp of the GPS data itself

                if lat and lon:
                    logger.info(f"Found GPS data for VIN {vin} at stage '{stage_name}': lat={lat}, lon={lon}, ts={gps_timestamp_str}")
                    # Max retries for geocoding can be a fixed small number, e.g., 2
                    formatted_address = await _reverse_geocode_location(session, str(lat), str(lon), max_retries=2)
                    return {
                        "vin": vin,
                        "latitude": lat,
                        "longitude": lon,
                        "formatted_address": formatted_address,
                        "timestamp_of_data": gps_timestamp_str, # Timestamp of the GPS fix
                        "data_found": True,
                        "stage_found": stage_name,
                        "api_status_code": gps_data_raw.get("statusCode"),
                        "api_message": gps_data_raw.get("message")
                    }
        logger.debug(f"No GPS data found for VIN {vin} at stage '{stage_name}'. Raw response: {str(gps_data_raw)[:200]}")
        # If no data in this stage, loop continues to the next, wider stage.
        # Update current_to_time for the next stage to keep looking further back if needed,
        # or simply rely on the expanding window from the original reference_timestamp_ms.
        # For this implementation, we'll keep the window expanding from the original reference_timestamp_ms.

    logger.warning(f"No GPS data found for VIN {vin} after all stages.")
    return {"vin": vin, "data_found": False}


if __name__ == "__main__":
    from src.last_active_handler import fetch_last_active_details # For standalone test

    async def main_ladh_test():
        test_vin_main = "P53AWDCC2CEA00079" # Using VIN P53AWDCC8CEA00068 as per feedback
        
        logger.info(f"Starting standalone test for last_available_data_handler with VIN: {test_vin_main}")

        conn = aiohttp.TCPConnector(limit_per_host=10)
        async with aiohttp.ClientSession(connector=conn) as session:
            # Step 1: Fetch actual last active timestamp for the test VIN
            logger.info(f"--- Fetching last active timestamp for {test_vin_main} ---")
            last_active_data = await fetch_last_active_details(session, test_vin_main, max_retries=2)
            
            if not last_active_data or last_active_data.get("data_available") != "Yes" or not last_active_data.get("timestamp"):
                logger.error(f"Could not fetch a valid last active timestamp for {test_vin_main}. Aborting further tests. Data: {last_active_data}")
                return

            last_active_ts_str_main = last_active_data["timestamp"]
            reference_ts_ms_main = string_timestamp_to_ms_epoch(last_active_ts_str_main)

            if reference_ts_ms_main is None:
                logger.error(f"Failed to convert fetched last active timestamp '{last_active_ts_str_main}' to ms epoch. Aborting test.")
                return
            
            logger.info(f"Using actual last active timestamp for {test_vin_main}: {last_active_ts_str_main} ({reference_ts_ms_main}ms epoch) as reference.")

            # Test Version Fetch
            logger.info("--- Testing Version Fetch ---")
            versions = await fetch_last_available_versions(session, test_vin_main, max_retries_overall=2)
            logger.info(f"Fetched versions for {test_vin_main}: {versions}")

            await asyncio.sleep(1) # Small delay if hitting same domain rapidly

            # Test Location Fetch
            logger.info("--- Testing Location Fetch ---")
            stages = app_config.STAGES_CONFIG
            location = await fetch_last_available_location(session, test_vin_main, reference_ts_ms_main, stages)
            logger.info(f"Fetched location for {test_vin_main}: {location}")

        logger.info("Standalone test for last_available_data_handler finished.")

    asyncio.run(main_ladh_test())