# src/config.py

# Stage configuration for data fetching
# Placeholder: Define actual stages as a list of dictionaries
STAGES_CONFIG = [
    {"name": "1_hour", "duration_spec": {"hours": 1}, "retries": 2},
    {"name": "6_hours", "duration_spec": {"hours": 6}, "retries": 3},
    {"name": "24_hours", "duration_spec": {"hours": 24}, "retries": 4},
    {"name": "3_days", "duration_spec": {"days": 3}, "retries": 5},
    {"name": "1_month", "duration_spec": {"days": 30}, "retries": 6}, # As per clarification
    {"name": "3_months", "duration_spec": {"days": 90}, "retries": 7}, # As per clarification
]

# Default feature flags
DEFAULT_FEATURE_FLAGS = {
    "network_timestamp": True,
    "connectivity": True,
    "gps": True,
    "rsrp": True,
    "horizontal_accuracy": True,
    "version_info": True,
    # Add other features as needed, e.g.:
    # "vcu_replacement": False,
    # "faults": False,
}

# Google API Scopes
GOOGLE_SCOPES = [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/spreadsheets'
]

# Master Google Sheet ID
MASTER_SHEET_ID = 'YOUR_MASTER_SHEET_ID_HERE'  # Replace with actual ID

# Sheet name for the VIN list in the master sheet
MASTER_VIN_LIST_SHEET_NAME = 'Sheet1'  # Replace with actual sheet name

# Pickle file name for caching request results
PICKLE_FILE_NAME = 'request_results.pickle'

# Logging Directory Constants
LOGS_BASE_DIR = 'logs'
MODULE_LOG_SUBDIR = 'module'

# API Base URLs and Endpoint Paths
OLA_TELEMATICS_BASE_URL = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics"
NOMINATIM_BASE_URL = "https://nominatim.openstreetmap.org/reverse"

NETWORK_TIMESTAMP_ENDPOINT = "/connectivityreport/timerange/networktimestamp"
CONNECTIVITY_DATA_ENDPOINT = "/adxtelematics/v2/getNetworkData"
GPS_TIMESTAMP_ENDPOINT = "/connectivityreport/timerange/gpstimestamp"
RSRP_ENDPOINT = "/connectivityreport/percentile/rsrp"
HORIZONTAL_ACCURACY_ENDPOINT = "/connectivityreport/percentile/horizontalaccuracy"
VERSION_INFO_ENDPOINT = "/vehicle/versioninfo"

# Add any other constants that were identified or need to be centralized
# Example: Default percentile values if they were constants
DEFAULT_RSRP_PERCENTILE = 75
DEFAULT_HORIZONTAL_ACCURACY_PERCENTILE = 50
