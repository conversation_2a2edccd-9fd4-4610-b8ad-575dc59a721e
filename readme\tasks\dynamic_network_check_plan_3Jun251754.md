# Plan to Enhance Network Status Checking Efficiency

**Objective:**
Refactor `module.py` to implement a staged, dynamic approach for checking network status for VINs, making the process more efficient by prioritizing recent data and progressively checking older data for misses. Subsequently, update the cron job frequency in `runner.py` to run every three hours.

**High-Level Design:**

The core idea is to process VINs in stages, where each stage represents a time window and a specific number of retries. If a VIN's data is found (a "hit"), it's excluded from subsequent, longer stages.

```mermaid
graph TD
    A[Start: Get All VINs] --> B{Loop Through Stages};
    B -- Next Stage --> C[Define Time Window & Retries for Stage];
    C --> D{Process Pending VINs for Current Stage};
    D -- Fetch Data --> E[Call async_batch_fetch_all with Stage Params];
    E --> F{Process Results};
    F -- Hit --> G[Store Hit Data, Remove VIN from Pending];
    F -- Miss --> H[Keep VIN in Pending for Next Stage];
    G --> I{Any Pending VINs?};
    H --> I;
    I -- Yes --> B;
    I -- No --> J[Aggregate All Stored Hit Data];
    J --> K[Save Aggregated Results (Pickle, Google Sheets)];
    K --> L[End];

    subgraph Stages
        direction LR
        S1[Stage 1: 1 Hr, 2 Retries]
        S2[Stage 2: 6 Hrs, 3 Retries]
        S3[Stage 3: 24 Hrs, 4 Retries]
        S4[Stage 4: 3 Days, 5 Retries]
        S5[Stage 5: 1 Month, 6 Retries]
        S6[Stage 6: 3 Months, 7 Retries]
    end
    B --> S1 --> S2 --> S3 --> S4 --> S5 --> S6;
```

**Detailed Changes:**

**1. `module.py` Modifications:**

   *   **Define Stage Configuration:**
      Introduce a class-level or global constant for stage definitions:
      ```python
      STAGES_CONFIG = [
          {"name": "1_hour", "duration_spec": {"hours": 1}, "retries": 2},
          {"name": "6_hours", "duration_spec": {"hours": 6}, "retries": 3},
          {"name": "24_hours", "duration_spec": {"hours": 24}, "retries": 4},
          {"name": "3_days", "duration_spec": {"days": 3}, "retries": 5},
          {"name": "1_month", "duration_spec": {"days": 30}, "retries": 6},
          {"name": "3_months", "duration_spec": {"days": 90}, "retries": 7},
      ]
      ```

   *   **Modify `NetworkTimestampChecker.async_fetch_with_retry()`:**
      *   Accept a new parameter `max_retries_for_stage: int`.
      *   Replace the hardcoded `MAX_RETRIES = 7` with this new parameter for the retry limit check (`if retry_count >= max_retries_for_stage:`).
      *   Update log messages to reflect `max_retries_for_stage`.

   *   **Modify `NetworkTimestampChecker.schedule_retry()`:**
      *   Accept `max_retries_for_stage: int` as a parameter.
      *   Update the log message: `print(f"Scheduling retry #{retry_count}/{max_retries_for_stage} for VIN {vin}")`.
      *   Pass `max_retries_for_stage` in the recursive call to `async_fetch_with_retry`.

   *   **Modify `async_fetch_<datatype>` methods:**
      (e.g., `async_fetch_network_timestamp()`, `async_fetch_connectivity_data()`, etc.)
      *   Each of these methods will need to accept `max_retries_for_stage: int`.
      *   They will pass this `max_retries_for_stage` down to their call to `self.async_fetch_with_retry()`.

   *   **Modify `NetworkTimestampChecker.async_batch_fetch_all()`:**
      *   Accept a new parameter `max_retries_for_stage: int`.
      *   When creating tasks for `asyncio.gather`, pass `max_retries_for_stage` to each `async_fetch_<datatype>` call.

   *   **Remove `NetworkTimestampChecker.get_time_range()`:**
      This method's logic will be replaced by dynamic calculations within `run_async`.

   *   **Refactor `NetworkTimestampChecker.run_async()`:**
      This method will orchestrate the new staged fetching logic. It will:
        *   Initialize `pending_vins` and `aggregated_results`.
        *   Iterate through `STAGES_CONFIG`.
        *   For each stage:
            *   Calculate `stage_from_time` based on `current_to_time` and `stage_config["duration_spec"]`.
            *   Call `self.async_batch_fetch_all()` with `pending_vins`, `stage_from_time`, `current_to_time`, and `stage_config["retries"]`.
            *   Process `stage_results`:
                *   For each "hit" (where data is not `None`):
                    *   If the VIN is not already in `aggregated_results` for that feature, add it.
                    *   Add the VIN to an `overall_hit_vins_this_stage` set.
            *   Update `pending_vins` by removing VINs from `overall_hit_vins_this_stage`.
            *   If `pending_vins` is empty, break the loop.
        *   After the loop, call `self.save_pickle_data(aggregated_results)` and `self.update_or_create_result_google_sheet(aggregated_results, self.master_sheet_id)`.

**2. `runner.py` Modifications:**

   *   **Update Cron Schedule:**
      Modify line 33 in `runner.py` from `minute='*/10'` to `hour='*/3', minute='0'`.

**Summary of Key Changes:**

*   **Staged Processing:** `run_async()` will loop through predefined stages.
*   **Dynamic Time Windows:** Calculated per stage.
*   **Dynamic Retries:** Passed down from stage config.
*   **VIN Exclusion:** "Hits" are excluded from subsequent stages.
*   **Aggregated Results:** Saved once at the end.
*   **Cron Update:** `runner.py` job scheduled for every 3 hours.