# Telematics Command Center Technical Documentation

## Tech Stack

### Frontend
- Next.js React Framework
- Material UI (MUI) Component Library
- CSS Modules for styling
- JavaScript/TypeScript

### UI Components
- MUI AppBar and Toolbar for navigation
- MUI Button components
- Custom date picker component
- Search functionality with vehicle ID input
- User modal with authentication

## API Endpoints & Authentication

### Required Cookies
- Must include New Relic monitoring cookies:
  - NREUM
  - NRAGENT 
  - JSESSIONID

### Authentication
- Session-based authentication required
- User credentials needed for API access

### Endpoints

#### New Relic Monitoring Endpoints
- Beacon URL: https://bam.nr-data.net
- Error Beacon URL: https://bam.nr-data.net
- License Key: NRJS-8643ab09d227db48846
- Application ID: **********
- Account ID: 3230001
- Trust Key: 362501
- Agent ID: **********

#### Application Endpoints
- Vehicle Search: `/scootersupportconsole/summaryconsolidated`
  - Accepts vehicle ID parameter (e.g. V53LMMMASAKA00073)
  - Requires authentication cookies
  - Returns vehicle telemetry data

#### Monitoring Configuration
- New Relic Browser Configuration:
  - Distributed Tracing Enabled
  - Privacy Cookies Enabled
  - AJAX Deny List: ["bam.nr-data.net"]
  - Browser Monitoring Enabled
  - Console Logging Enabled in Dev Mode

- Browser Monitoring Features:
  - Error Tracking
  - AJAX Request Monitoring 
  - Page Load Timing
  - JavaScript Error Reporting
  - Browser Interaction Traces

- Monitoring UI Elements:
  - Header Navigation Bar
  - Vehicle Search Form
  - User Authentication Modal
  - Vehicle Data Display
  - Error Messages

- Performance Metrics Tracked:
  - Page Load Time
  - API Response Time
  - UI Interaction Time
  - JavaScript Errors
  - Network Errors


