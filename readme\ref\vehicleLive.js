(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[5329], {37359: function (e, a, t) {
    "use strict";
    t.d(a, {W: function () {
      return n;
    }});
    t(32999);
    var r = t(72552);
    function n(e, a) {
      var t = [];
      return a && a.length > 0 && a.map(function (e) {
        var a = e in r.Z ? r.Z[e] : "";
        t.push(a);
      }), !(!a || !a.length || t.includes(e));
    }
  }, 41817: function (e, a, t) {
    "use strict";
    t.d(a, {Z: function () {
      return u;
    }});
    var r = t(92809), n = (t(67294), t(65295)), i = t(18515), s = t(42643), o = t(34548), l = t.n(o), c = t(38515), d = t(41120), _ = t(99695), h = t(85893), m = (0, d.Z)(function (e) {
      var a;
      return {root: {fontFamily: '"Trebuchet MS", Arial, Helvetica, sans-serif !important', "& .MuiDataGrid-cell": {fontSize: "13px", color: "#fff"}, "& .MuiDataGrid-columnHeaderTitle": {fontSize: "14px", fontWeight: "bold"}, "& .MuiDataGrid-footerContainer": {height: "25px", minHeight: "25px", color: "#fff"}, "& .super-app-theme--header": {backgroundColor: "#272727", color: "#fff"}, "& .MuiDataGrid-columnHeaderDraggableContainer": {width: "auto"}, "& .MuiDataGrid-toolbarContainer": {height: "30px", background: "#272727", position: "absolute", left: "-7px", top: "-38px"}, "& .MuiButton-label": (a = {fontFamily: '"Trebuchet MS", Arial, Helvetica, sans-serif', color: "white", fontWeight: "bold"}, (0, r.Z)(a, e.breakpoints.between("xs", "sm"), {fontSize: "10px"}), (0, r.Z)(a, e.breakpoints.up("sm"), {fontSize: "11px"}), a), "& .MuiButton-startIcon": {marginRight: "3px"}, "& .MuiTablePagination-root": {color: "#fff"}}};
    });
    function u(e) {
      var a = e.data && e.data.can_data && e.data.can_data.length ? e.data.can_data : [], t = [];
      a.map(function (e, a) {
        var r;
        t.push({id: a + 1 + "adx", time: (r = e.timeStamp, new Date(r).toLocaleString("en-US", {timeZone: "Asia/Kolkata"})), can_id: e.vcan_id, can_data: e.vcan_data});
      });
      var r = m(), o = t;
      return (0, h.jsx)("div", {children: (0, h.jsxs)(n.Z, {className: l().errorcardclass, children: [(0, h.jsxs)("div", {className: l().errorheader, children: [(0, h.jsx)(i.Z, {sx: {py: "0"}, title: "CAN Id: ".concat(e.canId, " "), className: l().errorheaderTitle}), (0, h.jsxs)("span", {className: l().errorcardheaderTimestamp, children: ["Start Time: ", e.startTime]})]}), t && t.length ? (0, h.jsx)(s.Z, {children: (0, h.jsxs)("div", {style: {height: "66vh", display: "flex", background: "#272727", width: "100%", marginTop: "30px"}, className: r.root, children: [(0, h.jsx)(_.Z, {styles: {"& .MuiGridPanel-paper": {minWidth: "217px !important", top: "-31px", position: "relative", "& .MuiFormLabel-root, .MuiInputBase-root, .MuiFormControlLabel-label, .MuiButton-label": {fontSize: "13px"}, "& .MuiDataGridPanelHeader-root": {padding: "4px"}, "& .MuiGridFilterForm-root": {paddingLeft: "4px", paddingRight: "4px", paddingTop: "0", paddingBottom: "0px", flexDirection: "column !important", "& .MuiGridFilterForm-columnSelect": {width: "210px"}, "& .MuiGridFilterForm-operatorSelect": {width: "210px"}, "& .MuiGridFilterForm-filterValueInput": {width: "210px"}, "& .MuiGridFilterForm-closeIcon": {alignItems: "flex-end"}}}}}), (0, h.jsx)(c._$r, {headerHeight: 25, showCellRightBorder: true, showColumnRightBorder: true, disableSelectionOnClick: true, disableColumnMenu: true, rows: o, columns: [{field: "time", headerName: "Time", width: 170, headerClassName: "super-app-theme--header"}, {field: "can_id", type: "string", headerName: "CAN ID", width: 100, headerClassName: "super-app-theme--header"}, {field: "can_data", headerName: "CAN Data", width: 230, headerClassName: "super-app-theme--header"}], pageSize: 25, rowsPerPageOptions: [100], rowHeight: 24, classes: {root: r.root}, components: {Toolbar: c.npt}})]})}) : (0, h.jsx)("div", {style: {paddingTop: "20px", paddingLeft: "20px", fontSize: "16px"}, children: (0, h.jsxs)("span", {children: ["No CAN data available for ", e.vid, " for the given period"]})})]})});
    }
  }, 90270: function (e, a, t) {
    "use strict";
    t.d(a, {Z: function () {
      return l;
    }});
    t(67294);
    var r = t(40821), n = t(59062), i = t(7671), s = t.n(i), o = t(85893);
    function l(e) {
      return (0, o.jsxs)(r.Z, {sx: {color: "#fff", position: "relative", zIndex: function (e) {
        return e.zIndex.drawer - 2;
      }}, open: e.spin, className: s().spinnerContainer, "data-testid": "spinnerBackDrop", children: [(0, o.jsx)(n.Z, {"data-testid": "circularSpinner", className: s().spinnerText, color: "grey"}), (0, o.jsx)("div", {"data-testid": "spinnerText", className: s().spinnerText, children: "Data is being prepared..."})]});
    }
  }, 30393: function (e, a, t) {
    "use strict";
    t.r(a), t.d(a, {default: function () {
      return b;
    }});
    var r = t(80318), n = t(67294), i = t(9008), s = t(34988), o = t(37359), l = t(11163), c = t(48662), d = t.n(c), _ = t(90270), h = (t(41817), t(9573)), m = t(38732), u = t(46926), p = t(97896), C = t(56011), g = t(3694), x = t(30381), f = t.n(x), v = t(85893);
    function b() {
      var e, a, t = (0, l.useRouter)(), c = (0, s.b)(), x = c.isCurrentPage, b = c.userPermission, T = c.vehicleId, j = c.bcmVehicleInfoDataADX, D = c.bcmVehicleInfoErrorADX, N = c.bcmVehicleInfoLoadingADX, Z = c.bmsVehicleInfoDataADXTime, S = c.bmsVehicleInfoDataADX, V = c.bmsVehicleInfoErrorADX, w = c.bmsVehicleInfoLoadingADX, R = c.rsrpDataLoadingADX, L = c.versionVehicleInfoDataADX, y = c.versionVehicleInfoErrorADX, M = c.versionVehicleInfoLoadingADX, E = c.faultsVehicleInfoDataADX, I = c.faultsVehicleInfoErrorADX, A = c.faultsVehicleInfoLoadingADX, H = c.basicVehicleInfoDataADX, B = c.basicVehicleInfoErrorADX, P = c.basicVehicleInfoLoadingADX, F = c.isVehicleInfoTriggered, k = c.vehicleConfigLoadingADX, G = c.vehicleConfigErrorADX, U = c.vehicleConfigDataADX, W = c.rsrpDataADX, O = c.isNepalUserADX, X = c.hillHoldDataADX, z = (0, r.Z)(O, 2), q = z[0], Y = (z[1], (0, r.Z)(b, 2)), Q = Y[0], K = (Y[1], (0, r.Z)(x, 2)), J = (K[0], K[1]), $ = (0, r.Z)(T, 1)[0], ee = (0, r.Z)(j, 2), ae = ee[0], te = (ee[1], (0, r.Z)(D, 2)), re = te[0], ne = (te[1], (0, r.Z)(N, 2)), ie = ne[0], se = (ne[1], (0, r.Z)(Z, 2)), oe = se[0], le = (se[1], (0, r.Z)(S, 2)), ce = le[0], de = (le[1], (0, r.Z)(V, 2)), _e = de[0], he = (de[1], (0, r.Z)(w, 2)), me = he[0], ue = (he[1], (0, r.Z)(R, 2)), pe = ue[0], Ce = (ue[1], (0, r.Z)(L, 2)), ge = Ce[0], xe = (Ce[1], (0, r.Z)(y, 2)), fe = xe[0], ve = (xe[1], (0, r.Z)(M, 2)), be = ve[0], Te = (ve[1], (0, r.Z)(E, 2)), je = Te[0], De = (Te[1], (0, r.Z)(I, 2)), Ne = De[0], Ze = (De[1], (0, r.Z)(A, 2)), Se = Ze[0], Ve = (Ze[1], (0, r.Z)(F, 2)), we = Ve[0], Re = (Ve[1], (0, r.Z)(H, 2)), Le = Re[0], ye = (Re[1], (0, r.Z)(B, 2)), Me = ye[0], Ee = (ye[1], (0, r.Z)(P, 2)), Ie = Ee[0], Ae = (Ee[1], (0, r.Z)(k, 2)), He = (Ae[0], Ae[1], (0, r.Z)(G, 2)), Be = (He[0], He[1], (0, r.Z)(U, 2)), Pe = Be[0], Fe = (Be[1], Le && Le.data ? Le.data : []), ke = ce && ce.data ? ce.data : [], Ge = ae && ae.data ? ae.data : [], Ue = ge && ge.data ? ge.data : [], We = je && je.data ? je.data : [], Oe = (0, r.Z)(W, 2), Xe = Oe[0], ze = (Oe[1], (0, r.Z)(X, 2)), qe = ze[0], Ye = (ze[1], We.filter(function (e) {
        return "1.0" == e.value;
      }).length), Qe = !!(ie || me || be || Se || Ie || pe), Ke = !!(re && _e && fe && Ne && Me && ie);
      (0, n.useEffect)(function () {
        (0, o.W)("Vehicle Live", Q) && t.push("/unauthorizedPage");
      }, [Q]), (0, n.useEffect)(function () {
        var e;
        e = localStorage.getItem("userLoginName") || "", newrelic.interaction().setAttribute("pageName", "Vehicle Live").setAttribute("userName", "" + e).save();
      }, []), (0, n.useEffect)(function () {
        window.location.pathname.includes("vehiclelive") && J("Vehicle Live");
      }, []);
      var Je, $e = {SOC_AH_USECAP_CELL: ["Usable Capacity per Cell", "Ah"], SAF_MV_UNDERVG_WARNG: ["Undervoltage Warning Trigger", "mV"], SAF_MV_UNDERVG_RESM: ["Undervoltage Resume Voltage", "mV"], SAF_MV_UNDERVG_ERROR: ["Undervoltage Error State", "mV"], SLC_MV_THDVTG_CHRGEND_CELL: ["Charge End Cell Voltage Threshold", "mV"], SAF_MV_CHG_OVERVG_ERROR: ["Charge Over Voltage Error", "mV"], SLC_MV_THDVTG_CCEND_CELL: ["CC End Cell Voltage Threshold", "mV"], battery_id: ["Battery ID", ""], parallel_cnt_cell_string: ["Cells connected in Parallel", ""], discharge_battery_temperature_warning: ["Discharge Battery Temp Warning", "°C"], discharge_battery_temperature_warning_rise: ["Discharge Battery Temp Warning Rise", "°C"], discharger_battery_temperature_error: ["Discharge Battery Temp Error", "°C"], charge_battery_temperature_warning: ["Charge Battery Temp Warning", "°C"], charger_battery_temperature_warning_rise: ["Charge Battery Temp Warning Rise", "°C"], charge_battery_temperature_error: ["Charge Battery Temp Error", "°C"], pdu_temperature_warning: ["PDU Temp Warning", "°C"], pdu_temperature_warning_delta: ["PDU Temp Warning Delta", "°C"], pdu_temperature_error: ["PDU Temp Error", "°C"], pdu_temperature_cut_off: ["PDU Temp Cut Off", "°C"], cv_charging_cutoff_timer: ["CV Charging Cutoff Timer", "min"], sleep_battery_temperature_warning: ["Sleep Battery Temp Warning", "°C"], sleep_battery_temperature_warning_rise: ["Sleep Battery Temp Warning Rise", "°C"], cell_min_deviation_warning: ["Cell min Deviation Warning", "mV"], voltage_diff_for_complimentary_spike: ["Voltage Diff for Complimentary Spike", "mV"], discharge_mode_current_limit: ["Discharge Mode Current Limit", "Amp"], SAF_PCT_SOCREGENALLOWED: ["Regen Fault SOC limit", "%"], SAF_DEGC_REGENALLOWED: ["Regen Fault Temperature limit", "°C"], saf_cnt_errorcntr_shrtcrct: ["Short Circuit Err Cnt for Permnt fault", ""], SAF_CNT_OVERVLTGCHRGPERM: ["Over Voltage Err Cnt for Permnt fault", ""], SAF_CNT_ERRORCNTR_CELLDIP: ["Cell Dip Fault Count to Set Error", ""], SAF_CNT_ERRORCNTR_COMPSPIKE: ["Cell Comp Spike Detectn to set Error", "mV"], SAF_DEGC_TEMPRISEPDU: ["PDU Temp Rise CutOff", "°C"], slc_aovercurr_tolchg: ["Over Current Charge Fault Thresh", "Amp"]}, ea = {cruise_control: ["Cruise Control", ""], mcu_temp_warning: ["MCU Warn Normal Set Temp", "°C"], mcu_temp_error: ["MCU Temp Thresh for eco mode Transitn", "°C"], mcu_temp_cut_off: ["MCU Temp Thresh for Park mod Transitn", "°C"], motor_temp_warning: ["Motor Temp Thresh for Normal Warn", "°C"], motor_temp_error: ["Motor Temp Thresh for eco mod Transitn", "°C"], motor_temp_cut_off: ["Motor Temp Thresh - Park mode Transitn", "°C"]}, aa = {limp_home_mode: "Limp Home Mode", Hill_Hold: "Hill Hold", Take_Me_Home_Lights: "Take me Home lights", Vehicle_type: "Vehicle Type"}, ta = {"1.0": "S1", "2.0": "S1 Pro wo upgrade", "3.0": "S1 Pro with upgrade"}, ra = {0: "Not Present", 1: "Present"}, na = {0: "Not Present", 1: "Present"}, ia = {0: "Not Present", 1: "Present"}, sa = {0: "Not Present", 1: "Present"};
      return (0, v.jsxs)(v.Fragment, {children: [(0, v.jsxs)(i.default, {children: [(0, v.jsx)("title", {children: "Telematics Command Center"}), (0, v.jsx)("meta", {name: "description", content: "Telematics Command Center"}), (0, v.jsx)("link", {rel: "icon", href: "/favicon.ico"})]}), (0, v.jsx)("div", {className: d().chartClass, children: $ ? we ? Qe ? (0, v.jsx)("div", {className: d().initialText, children: (0, v.jsx)(_.Z, {spin: Qe})}) : Ke ? (0, v.jsx)("div", {className: d().initialText, children: "`Something went wrong, please try again!!!`"}) : (0, v.jsxs)(v.Fragment, {children: [(0, v.jsxs)("div", {className: d().vehicleInfomainHeader, children: [(0, v.jsxs)("span", {children: ["VIN:", " ", (0, v.jsx)("span", {style: {fontWeight: "600"}, children: $}), " "]}), (0, v.jsxs)("span", {children: ["Report Loaded at", " ", (0, v.jsx)("span", {style: {fontSize: "14px"}, children: oe ? (Je = oe, new Date(Je).toLocaleString("en-US", {timeZone: "Asia/Kolkata", year: "numeric", month: "numeric", day: "numeric", hour: "numeric", minute: "numeric", second: "numeric"})) : ""}), " "]})]}), (0, v.jsxs)("div", {className: d().vehicleInfoDiv, children: [Fe ? (0, v.jsx)("div", {className: d().outerContTab1, children: (0, v.jsx)(p.Z, {className: d().firstTableContainer, children: (0, v.jsxs)(h.Z, {sx: {minWidth: 150}, size: "small", "aria-label": "a dense table", className: d().firstTable, children: [(0, v.jsx)(C.Z, {children: (0, v.jsx)(g.Z, {children: (0, v.jsx)(u.Z, {align: "left", colSpan: 2, className: d().tableHeader, children: "Vehicle Details"})})}), (0, v.jsxs)(m.Z, {children: [(0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Scooter Model"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: Fe.vehicle_variant})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Purchase Date"}), Fe.purchased_date ? (0, v.jsx)(u.Z, {className: d().tableRowRight, children: Fe.purchased_date.replace(",", "")}) : (0, v.jsx)(u.Z, {className: d().tableRowRight, children: "-"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Manufacturing Date"}), Fe.make ? (0, v.jsx)(u.Z, {className: d().tableRowRight, children: Fe.make[0].toUpperCase() + Fe.make.slice(1).toLocaleLowerCase().replace("-", " ")}) : (0, v.jsx)(u.Z, {className: d().tableRowRight, children: "-"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, style: {border: "none"}, children: "S/W version"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, style: {border: "none"}, children: Ue.length ? Ue[0].vehicle_software_version : " "})]})]})]})})}) : (0, v.jsx)("div", {className: d().cardDiv, children: (0, v.jsx)("p", {className: d().cardText, children: "Vehicle Details not available"})}), ke.length || Ge.length ? (0, v.jsx)("div", {className: d().outerContTab2, children: (0, v.jsx)(p.Z, {className: d().secondTableContainer, children: (0, v.jsxs)(h.Z, {sx: {minWidth: 150}, size: "small", "aria-label": "a dense table", className: d().secondTable, children: [(0, v.jsx)(C.Z, {children: (0, v.jsx)(g.Z, {children: (0, v.jsx)(u.Z, {align: "left", colSpan: 2, className: d().tableHeader, children: "Latest Vehicle Data"})})}), (0, v.jsxs)(m.Z, {children: [(0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "State"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: me ? (0, v.jsx)("span", {style: {background: "#161616"}, children: "Data loading.."}) : null !== ke && undefined !== ke && null !== (e = ke[0]) && undefined !== e && e.scooter_state ? null === ke || undefined === ke || null === (a = ke[0]) || undefined === a ? undefined : a.scooter_state : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "SOC"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: me ? (0, v.jsx)("span", {children: "Data loading.."}) : ke.length ? (0, v.jsxs)(v.Fragment, {children: [ke[0].pack_soc, "%"]}) : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Remaining Range"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: ie ? (0, v.jsx)("span", {children: "Data loading.."}) : Ge.length ? (0, v.jsxs)(v.Fragment, {children: [Ge[0].remaining_range, " km"]}) : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Odometer"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: ie ? (0, v.jsx)("span", {children: "Data loading.."}) : Ge.length ? (0, v.jsxs)(v.Fragment, {children: [Ge[0].vehicle_odo, " km"]}) : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "Time to Full Charge"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: me ? (0, v.jsx)("span", {children: "Data loading.."}) : ke.length ? (0, v.jsxs)(v.Fragment, {children: [ke[0].time_charge_full_sc_1, " min"]}) : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: "LTE Connectivity"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: pe ? (0, v.jsx)("span", {children: "Data loading.."}) : Xe && Xe.signal_rsrp ? Xe.signal_rsrp[0].toUpperCase() + Xe.signal_rsrp.slice(1).toLowerCase() : "--"})]}), (0, v.jsxs)(g.Z, {children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, style: {border: "none"}, children: "Last Connected Timestamp"}), (0, v.jsx)(u.Z, {className: d().tableRowRight, style: {border: "none"}, children: me ? (0, v.jsx)("span", {children: "Data loading.."}) : ke.length ? f()(ke[0].timestamp).format("Do MMM YYYY, h:mm:ss A") : "--"})]})]})]})})}) : ie && me ? (0, v.jsx)("div", {className: d().outerContTab2, children: (0, v.jsx)("p", {className: d().cardText, style: {background: "#272727", color: "#fff"}, children: "Latest Vehicle data is loading"})}) : (0, v.jsx)("div", {className: d().outerContTab2, children: (0, v.jsx)("p", {className: d().cardText, style: {background: "#272727", color: "#fff"}, children: "Latest Vehicle data not available"})}), (0, v.jsx)("div", {className: d().faultcardDiv, children: We.length ? (0, v.jsxs)(v.Fragment, {children: [(0, v.jsx)("p", {className: d().cardText, style: {color: "#ff8d00", fontSize: "30px"}, children: Ye}), (0, v.jsx)("p", {className: d().cardHeader, style: {color: "#979797"}, children: "Faults reported Today"})]}) : Se ? (0, v.jsx)("p", {className: d().cardText, style: {color: "#979797"}, children: "Faults data Loading.."}) : (0, v.jsx)("p", {className: d().cardText, style: {color: "#979797"}, children: "NO Faults reported today"})})]}), !q && (0, v.jsxs)("div", {className: d().versionConfigDiv, children: [(0, v.jsx)("div", {className: d().outerContTab1, children: (0, v.jsx)(p.Z, {className: d().firstTableContainer, children: (0, v.jsxs)(h.Z, {sx: {minWidth: 150}, size: "small", "aria-label": "a dense table", className: d().firstTable, children: [(0, v.jsx)(C.Z, {children: (0, v.jsx)(g.Z, {children: (0, v.jsx)(u.Z, {align: "left", colSpan: 2, className: d().tableHeader, children: "BMS Config"})})}), (0, v.jsx)(m.Z, {children: Object.keys($e).map(function (e, a) {
        return (0, v.jsxs)(g.Z, {className: d().tableRow, children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: $e[e][0]}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: Pe && Pe[e.toLocaleLowerCase()] && Pe[e.toLocaleLowerCase()] + " " + $e[e][1] || "-"})]}, a);
      })})]})})}), (0, v.jsx)("div", {className: d().outerContTab2, children: (0, v.jsx)(p.Z, {className: d().secondTableContainer, children: (0, v.jsxs)(h.Z, {sx: {minWidth: 150}, size: "small", "aria-label": "a dense table", className: d().secondTable, children: [(0, v.jsx)(C.Z, {children: (0, v.jsx)(g.Z, {children: (0, v.jsx)(u.Z, {align: "left", colSpan: 2, className: d().tableHeader, children: "MCU Config"})})}), (0, v.jsx)(m.Z, {children: Object.keys(ea).map(function (e, a) {
        return (0, v.jsxs)(g.Z, {className: d().tableRow, children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: ea[e][0]}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: Pe && Pe[e.toLocaleLowerCase()] && Pe[e.toLocaleLowerCase()] + " " + ea[e][1] + ("cruise_control" === e ? "(".concat(ra[Pe[e]], ")") : "") || "-"})]}, a);
      })})]})})}), (0, v.jsx)("div", {className: d().outerContTab3, children: (0, v.jsx)(p.Z, {className: d().thirdTableContainer, children: (0, v.jsxs)(h.Z, {sx: {minWidth: 150}, size: "small", "aria-label": "a dense table", className: d().firstTable, children: [(0, v.jsx)(C.Z, {children: (0, v.jsx)(g.Z, {children: (0, v.jsx)(u.Z, {align: "left", colSpan: 2, className: d().tableHeader, children: "VCU Config"})})}), (0, v.jsx)(m.Z, {children: Object.keys(aa).map(function (e, a) {
        return (0, v.jsxs)(g.Z, {className: d().tableRow, children: [(0, v.jsx)(u.Z, {className: d().tableRowLeft, children: aa[e]}), (0, v.jsx)(u.Z, {className: d().tableRowRight, children: ("Hill_Hold" === e ? (null === qe || undefined === qe ? undefined : qe.value) || "0" : Pe && (null === Pe || undefined === Pe ? undefined : Pe[null === e || undefined === e ? undefined : e.toLocaleLowerCase()]) && (null === Pe || undefined === Pe ? undefined : Pe[null === e || undefined === e ? undefined : e.toLocaleLowerCase()])) + ("Vehicle_type" === e ? " (".concat(null === ta || undefined === ta ? undefined : ta[null === Pe || undefined === Pe ? undefined : Pe[null === e || undefined === e ? undefined : e.toLocaleLowerCase()]], ")") : "limp_home_mode" === e ? " (".concat(null === na || undefined === na ? undefined : na[null === Pe || undefined === Pe ? undefined : Pe[null === e || undefined === e ? undefined : e.toLocaleLowerCase()]], ")") : "Hill_Hold" === e ? " (".concat(null === ia || undefined === ia ? undefined : ia[Number(null === qe || undefined === qe ? undefined : qe.value) || 0], ")") : "Take_Me_Home_Lights" === e ? " (".concat(null === sa || undefined === sa ? undefined : sa[null === Pe || undefined === Pe ? undefined : Pe[null === e || undefined === e ? undefined : e.toLocaleLowerCase()]], ")") : "") || "-"})]}, a);
      })})]})})})]})]}) : (0, v.jsx)("div", {className: d().initialText, children: "Please click submit to view data"}) : (0, v.jsx)("div", {className: d().initialText, children: "Please search with Vehicle ID"})})]});
    }
  }, 36955: function (e, a, t) {
    (window.__NEXT_P = window.__NEXT_P || []).push(["/scootersupportconsole/vehiclelive", function () {
      return t(30393);
    }]);
  }, 34548: function (e) {
    e.exports = {errorcardclass: "errors_errorcardclass__34PXe", errorheader: "errors_errorheader__3qCxr", errorcardheaderTimestamp: "errors_errorcardheaderTimestamp__1omTU", errorheaderTitle: "errors_errorheaderTitle__2pzH2", errorCountcls: "errors_errorCountcls__3EOBL", modalDiv: "errors_modalDiv__2Jo4B", modalHeader: "errors_modalHeader__3LTB2", closeBtn: "errors_closeBtn__33UJ1", vinSection: "errors_vinSection__VSMDQ", viewBtn: "errors_viewBtn__13ckN", createGroupBtn: "errors_createGroupBtn__syMCp", downloadBtn: "errors_downloadBtn__1jB12", cancelBtn: "errors_cancelBtn__BYvr3"};
  }, 7671: function (e) {
    e.exports = {spinnerText: "spinner_spinnerText__3z6uU", spinnerContainer: "spinner_spinnerContainer__3cqZ0"};
  }, 48662: function (e) {
    e.exports = {chartClass: "charts_chartClass__144o9", initialText: "charts_initialText__V9LJ-", canDataMainCls: "charts_canDataMainCls__3OMpy", canDataMainRS: "charts_canDataMainRS__2WhkD", canDataMainLS: "charts_canDataMainLS__9qqc9", canDataHeader: "charts_canDataHeader__2Kt7G", canDataRS: "charts_canDataRS__1K9jQ", canDataCont: "charts_canDataCont__1zRyq", canDataSubCont: "charts_canDataSubCont__2g_JF", canDataContent: "charts_canDataContent__1ISCM", detailBtn: "charts_detailBtn__3DDbm", CanCommandCont: "charts_CanCommandCont__QueFB", CanCommandHeader: "charts_CanCommandHeader__26nfn", CanCommandVinSec: "charts_CanCommandVinSec__2cuN7", CanCommandVinForm: "charts_CanCommandVinForm__2m5yD", CanCommandVinFormInput: "charts_CanCommandVinFormInput__3fZWP", CanCommandGetUuidSec: "charts_CanCommandGetUuidSec__1TLZP", CanCommandGetUuid: "charts_CanCommandGetUuid__3CEFL", CanCommandInputSecShow: "charts_CanCommandInputSecShow__Hj1ge", CanCommandInputdiv: "charts_CanCommandInputdiv__dxQMq", CanCommandInputSec: "charts_CanCommandInputSec__3Aowq", CanCommandBtn: "charts_CanCommandBtn__M1ySG", mainHeader: "charts_mainHeader__2k__3", connectivityDiv: "charts_connectivityDiv__23PjQ", cardDiv: "charts_cardDiv__3jERA", cardHeader: "charts_cardHeader__16SzL", cardText: "charts_cardText__3jgjx", rightDiv: "charts_rightDiv__1DB4U", rightDivText: "charts_rightDivText__CmgfB", vehicleInfomainHeader: "charts_vehicleInfomainHeader__3eW2p", vehicleInfoDiv: "charts_vehicleInfoDiv__2Mupz", faultcardDiv: "charts_faultcardDiv__2BsSU", outerContTab1: "charts_outerContTab1__2Y_yl", outerContTab2: "charts_outerContTab2__293Kp", firstTable: "charts_firstTable__6xS9V", secondTable: "charts_secondTable__3ISc3", firstTableContainer: "charts_firstTableContainer__1v9z8", tableHeader: "charts_tableHeader__16ELr", tableRowLeft: "charts_tableRowLeft__1W2d9", tableRowRight: "charts_tableRowRight__259G_", secondTableContainer: "charts_secondTableContainer__3Uopq", versionConfigDiv: "charts_versionConfigDiv__avRTa", outerContTab3: "charts_outerContTab3__2-oPD", tableRow: "charts_tableRow__3l28B", thirdTableContainer: "charts_thirdTableContainer__2PXp_", vehicleInfoDivConnectivity: "charts_vehicleInfoDivConnectivity__3yRNd", uploadSection: "charts_uploadSection__2xStB", mainHeading: "charts_mainHeading__Ku6aI", exportSection: "charts_exportSection__3dElX", dataTypeSelection: "charts_dataTypeSelection__1SVhR", groupVin_leftCont: "charts_groupVin_leftCont__16qIr", groupVin_dataTypeError: "charts_groupVin_dataTypeError__1pUGa", groupVin_dataTypeSelection: "charts_groupVin_dataTypeSelection__BCW5z", groupVin_middleCont: "charts_groupVin_middleCont__2wp0u", groupVin_rightCont: "charts_groupVin_rightCont__2G3DP", groupVINDatePickerShow: "charts_groupVINDatePickerShow__1jzyw", groupVINDatePickerHide: "charts_groupVINDatePickerHide__3iqsS", errorText: "charts_errorText__WAlxQ", leftCont: "charts_leftCont__1_Sfy", dataTypeError: "charts_dataTypeError__2naG5", middleCont: "charts_middleCont__38gHY", rightCont: "charts_rightCont__3X1Y9", searchOuterCont: "charts_searchOuterCont__WhH4c", searchContainer: "charts_searchContainer__1mQb4", invalidText: "charts_invalidText__2PBZh", exportNoteSection: "charts_exportNoteSection__1OJ7e", selfAnalysissubmitButton: "charts_selfAnalysissubmitButton__3x2A-", inputVin: "charts_inputVin__O1Skp", resetCls: "charts_resetCls__3bYea", groupVinSubmitButton: "charts_groupVinSubmitButton__FdVjP", groupVinClearButton: "charts_groupVinClearButton__1Qkcm", sampleFile: "charts_sampleFile__3oVT3", downloadLink: "charts_downloadLink__2Cqvz", noteSection: "charts_noteSection__2FSM3", theftAlarmForm: "charts_theftAlarmForm__3SOQC", submitBtnDiv: "charts_submitBtnDiv__1ocqs", submitBtn: "charts_submitBtn__Zy3Nv", noAccessPage: "charts_noAccessPage__2UiGs", selectGrpCls: "charts_selectGrpCls__1k17q", selectGrpToggle: "charts_selectGrpToggle__3bScv", selectGrpMenu: "charts_selectGrpMenu__3fFxn", groupVinCreate_leftCont: "charts_groupVinCreate_leftCont__YqoNL", groupVinCreate_middleCont: "charts_groupVinCreate_middleCont__CXqo1", groupVinCreate_rightCont: "charts_groupVinCreate_rightCont__2ZNf8", groupVinCreateInputName: "charts_groupVinCreateInputName__ijVF3", groupVinCreateInputVin: "charts_groupVinCreateInputVin__16X9J", groupVinCreateSubmitButton: "charts_groupVinCreateSubmitButton__lc20M", groupVinCont: "charts_groupVinCont__1nzDP", labelWidth: "charts_labelWidth__2wmRn", noteText: "charts_noteText__3xey-", connectivityHeader: "charts_connectivityHeader__niF6Z"};
  }}, function (e) {
    e.O(0, [3174, 6227, 1457, 6206, 9774, 2888, 179], function () {
      return a = 36955, e(e.s = a);
      var a;
    });
    var a = e.O();
    _N_E = a;
  }]);
  