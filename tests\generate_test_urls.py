import time
from datetime import datetime, timedelta

VINS_TO_CHECK = [
    "P53BXDCC6EAA00016",
    "P53BADCA4CJA00027",
    "P53BTDCC8EBA00007",
]

STAGES_CONFIG = [
    {"name": "1_hour", "duration_spec": {"hours": 1}},
    {"name": "6_hours", "duration_spec": {"hours": 6}},
    {"name": "24_hours", "duration_spec": {"hours": 24}},
    {"name": "3_days", "duration_spec": {"days": 3}},
    {"name": "1_month", "duration_spec": {"days": 30}},
    {"name": "3_months", "duration_spec": {"days": 90}},
]

DEFAULT_FEATURE_FLAGS = {
    'network_timestamp': True,
    'connectivity': True,
    'gps': True,
    'rsrp': True,
    'horizontal_accuracy': True,
    'version_info': True
}

BASE_URLS = {
    "network_timestamp": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/networktimestamp",
    "connectivity": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getNetworkData",
    "gps": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp",
    "rsrp": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/rsrp",
    "horizontal_accuracy": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/horizontalaccuracy",
    "version_info": "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/vehicle/versioninfo"
}

OUTPUT_FILE_PATH = "results/generated_urls.txt"

def main():
    current_to_time_ms = int(time.time() * 1000)

    with open(OUTPUT_FILE_PATH, "w") as outfile:
        for vin in VINS_TO_CHECK:
            print(f"\n# VIN: {vin}") # Stays on console
            outfile.write(f"# VIN: {vin}\n") # Also write to file for context
            for stage_config in STAGES_CONFIG:
                stage_name = stage_config["name"]
                duration_spec = stage_config["duration_spec"]
                
                time_delta = timedelta(**duration_spec)
                from_time_offset_ms = int(time_delta.total_seconds() * 1000)
                
                stage_from_time_ms = current_to_time_ms - from_time_offset_ms
                
                stage_header = f"\n## Stage: {stage_name} (From: {stage_from_time_ms}, To: {current_to_time_ms})"
                print(stage_header) # Stays on console
                outfile.write(stage_header + "\n") # Also write to file for context

                for feature_name, is_enabled in DEFAULT_FEATURE_FLAGS.items():
                    if is_enabled:
                        base_url = BASE_URLS[feature_name]
                        url = ""
                        if feature_name == "version_info":
                            url = f"{base_url}?vin={vin}"
                        else:
                            url = f"{base_url}?vehicleId={vin}&fromTime={stage_from_time_ms}&toTime={current_to_time_ms}"
                            if feature_name == "rsrp":
                                url += "&percentile=75"
                            elif feature_name == "horizontal_accuracy":
                                url += "&percentile=50"
                        outfile.write(url + "\n")
        print(f"\nGenerated URLs written to {OUTPUT_FILE_PATH}")


if __name__ == "__main__":
    main()