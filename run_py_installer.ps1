# PowerShell script to run network check immediately with SSL bypass
# Define available scripts
$availableScripts = @("runner.py", "module.py", "async_simple.py")

# Display options to the user
Write-Host "Please select a script to run with PyInstaller:"
for ($i = 0; $i -lt $availableScripts.Length; $i++) {
    Write-Host ("{0}. {1}" -f ($i + 1), $availableScripts[$i])
}

# Get user input
$selectedIndex = 0
while ($selectedIndex -lt 1 -or $selectedIndex -gt $availableScripts.Length) {
    try {
        $userInput = Read-Host "Enter the number of the script"
        $selectedIndex = [int]$userInput
        if ($selectedIndex -lt 1 -or $selectedIndex -gt $availableScripts.Length) {
            Write-Warning "Invalid selection. Please enter a number between 1 and $($availableScripts.Length)."
        }
    }
    catch {
        Write-Warning "Invalid input. Please enter a number."
    }
}

$TargetScriptFile = $availableScripts[$selectedIndex - 1]
$TargetScriptName = $TargetScriptFile.Replace(".py", "")

# Set environment variables to bypass SSL certificate verification
$env:PYTHONHTTPSVERIFY = "0"
$env:REQUESTS_CA_BUNDLE = ""
$env:SSL_CERT_FILE = ""
$env:PYTHONWARNINGS = "ignore:Unverified HTTPS request"

# Add additional SSL bypass for aiohttp
$env:AIOHTTP_NO_EXTENSIONS = "1"

Write-Host "Running pyinstaller for $TargetScriptFile..."

# Run the module directly (bypassing the scheduler)
uv run pyinstaller "$TargetScriptFile"

Write-Host "pyinstaller for $TargetScriptFile completed."

# Update run_hidden_cron.vbs
$vbsPath = "run_hidden_cron.vbs"
$newExecutablePath = "\dist\$TargetScriptName\$TargetScriptName.exe"
Write-Host "Updating $vbsPath to point to $newExecutablePath..."

try {
    $vbsContent = Get-Content $vbsPath -Raw
    $oldLinePattern = 'WshShell\.Run chr\(34\) & CreateObject\("WScript\.Shell"\)\.CurrentDirectory & ".*?\.exe" & chr\(34\), 0'
    $newLine = 'WshShell.Run chr(34) & CreateObject("WScript.Shell").CurrentDirectory & "' + $newExecutablePath + '" & chr(34), 0'
    
    if ($vbsContent -match $oldLinePattern) {
        $updatedVbsContent = $vbsContent -replace $oldLinePattern, $newLine
        Set-Content -Path $vbsPath -Value $updatedVbsContent -Force
        Write-Host "$vbsPath updated successfully."
    } else {
        Write-Warning "Could not find the line to update in $vbsPath. Manual update might be required."
    }
}
catch {
    $exceptionMessage = $_.Exception.Message
    Write-Error ("Error updating {0}: {1}" -f $vbsPath, $exceptionMessage)
}