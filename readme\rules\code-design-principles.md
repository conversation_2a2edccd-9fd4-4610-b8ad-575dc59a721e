# Code Design Principles

## Objective
Create modular, maintainable, and extensible Python code organized in a clear structure (`module_name/` with `core.py`, `interfaces/`, `utils.py`, `helpers.py`) to ensure separation of concerns, reusability, and scalability.

## Core Principles (SOLID)
1. **Single Responsibility Principle (SRP)**  
   Each module, class, or function must have one well-defined responsibility.  
   *Example*: `core.py` handles business logic, `interfaces/cli.py` manages CLI interactions, `utils.py` provides reusable utilities.

2. **Open/Closed Principle (OCP)**  
   Modules and classes should be open for extension (e.g., via subclassing or plugins) but closed for modification.  
   *Example*: Add new features to `core.py` via subclasses or new modules without changing existing code.

3. **Liskov Substitution Principle (LSP)**  
   Subclasses must be substitutable for their base classes without breaking functionality.  
   *Example*: A custom CLI in `interfaces/cli.py` must adhere to the base interface used by `core.py`.

4. **Interface Segregation Principle (ISP)**  
   Clients should only depend on interfaces they use. Avoid bloated interfaces.  
   *Example*: Separate `interfaces/cli.py` and `interfaces/ui.py` to ensure each depends only on relevant abstractions.

5. **Dependency Inversion Principle (DIP)**  
   High-level modules (e.g., `core.py`) should depend on abstractions, not low-level modules (e.g., `interfaces/cli.py`).  
   *Example*: Use protocols or abstract base classes in `core.py` to define interfaces for `interfaces/` modules.

## Modular Structure
- **Folder Structure**:  
  ```
  module_name/
  ├── __init__.py        # Expose public APIs, minimal logic
  ├── core.py            # Business logic and domain models
  ├── interfaces/        # Interface-specific logic
  │   ├── cli.py         # CLI interface
  │   ├── ui.py          # GUI or web interface
  │   └── api.py         # API endpoints (optional)
  ├── utils.py           # Reusable, stateless utilities
  └── helpers.py         # Module-specific helpers
  ```
- **Roles**:  
  - `core.py`: Core logic and abstractions (e.g., protocols, base classes).  
  - `interfaces/`: Isolated interface implementations (CLI, GUI, API).  
  - `utils.py`: General-purpose, stateless functions.  
  - `helpers.py`: Module-specific utilities or wrappers.  
- **Guidelines**:  
  - Use `__init__.py` to define public APIs and hide internals (e.g., prefix with `_`).  
  - Avoid circular imports; prefer dependency injection or abstractions.  
  - Include docstrings, type hints, and unit tests (e.g., `tests/test_core.py`).  
  - Design for extensibility (e.g., support new interfaces via `interfaces/api.py`).  

## Additional Rules
1. **Naming Consistency**: Use clear, specific names (e.g., `cli.py`, not `interface_cli.py`).  
2. **Encapsulation**: Expose minimal APIs via `__init__.py`.  
3. **Scalability**: Support new interfaces or features without restructuring.  
4. **Portability**: Use relative imports and avoid hardcoded configurations.

## Rationale
This design ensures clarity, flexibility, and testability while adhering to SOLID principles, making modules easy to maintain and extend.