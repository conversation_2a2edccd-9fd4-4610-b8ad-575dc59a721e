# PowerShell script to run network check immediately with SSL bypass
# Set environment variables to bypass SSL certificate verification
$env:PYTHONHTTPSVERIFY = "0"
$env:REQUESTS_CA_BUNDLE = ""
$env:SSL_CERT_FILE = ""
$env:PYTHONWARNINGS = "ignore:Unverified HTTPS request"

# Add additional SSL bypass for aiohttp
$env:AIOHTTP_NO_EXTENSIONS = "1"

Write-Host "Starting network check now..."

# Run the module directly (bypassing the scheduler)
uv run module.py

Write-Host "Network check completed." 