from apscheduler.schedulers.asyncio import AsyncIOScheduler
# module.NetworkTimestampChecker will be used by async_simple
from async_simple import main as async_simple_main_job
import logging
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_checker.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    try:
        # Create the scheduler
        scheduler = AsyncIOScheduler()
        
        # Add the job to run async_simple.main every 3 hours
        scheduler.add_job(async_simple_main_job, 'cron', hour='*/3', minute='0')
        
        scheduler.start()
        logger.info("Scheduler started, will run async_simple.main every 3 hours.")
        
        # Keep the main coroutine running
        while True:
            await asyncio.sleep(60)
    except (KeyboardInterrupt, SystemExit):
        logger.info("Runner stopped")
        if scheduler.running:
            scheduler.shutdown()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("Runner stopped due to interrupt.")