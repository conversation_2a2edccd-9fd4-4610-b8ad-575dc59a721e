# Detailed Phased Plan: Enhanced Vehicle Data Reporting Modules

**Version:** 1.5
**Date:** 2025-06-08

## 1. Objective

To implement a modular and efficient system for fetching, processing, and reporting vehicle telematics data. This plan outlines the creation of new Python handlers and modules to:
*   Reliably fetch a vehicle's "last active" timestamp and associated details.
*   Dynamically retrieve the "last available" version information (BMS, MCU, BCM, Software) and location data, using a staged approach to minimize "N/A" values.
*   Centralize data formatting logic.
*   Streamline Google Sheets integration by populating a single, consolidated report.

This project involves the creation of four new Python files within the `src/` directory: `last_active_handler.py`, `last_available_data_handler.py`, `format_handler.py`, and `gsheet_handler.py`.

## 2. Guiding Principles

*   **Modularity:** Each new module will have a distinct responsibility.
*   **Reusability:** Functions within these modules should be reusable where appropriate.
*   **Clarity:** Code should be clear, well-documented, and easy to understand.
*   **Efficiency:** Optimize data fetching by first getting the last active timestamp and then targeting subsequent queries.
*   **Data Completeness:** Prioritize finding "last available" data to minimize "N/A" entries in the final report.
*   **Simplified Reporting:** Consolidate output into a single Google Sheet.
*   **Testability:** Each phase and module should be testable (e.g., via `pytest`).
*   **Standalone Testability:** Each new Python module file should include a `if __name__ == "__main__":` block for basic, standalone execution to demonstrate or test its primary functions. This block should use a realistic test VIN when interacting with live APIs.
*   **Robust Imports:** Modules should directly import necessary configurations (e.g., `from src import config`). Failures in importing critical configurations should result in a standard Python `ImportError`, rather than using internal fallbacks or mocks within the module itself for runtime.
*   **Standardized Timestamps:** All timestamps presented in the final report or used for display should be converted to a consistent, human-readable ISO-like format (e.g., `YYYY-MM-DD HH:MM:SS` or `YYYY-MM-DDTHH:MM:SS`).

## 3. Proposed File Structure Additions

```
telematics_command_center/
|-- src/
|   |-- __init__.py
|   |-- last_active_handler.py
|   |-- last_available_data_handler.py
|   |-- format_handler.py
|   |-- gsheet_handler.py
|   |-- config.py 
|   |-- (potentially other refactored modules like api_clients.py)
|-- (other existing files and directories)
```
*Note: This plan focuses on the creation and functionality of the four specified handler/module files. These may leverage other common modules (e.g., for configuration, core API client logic) if they are created as part of broader refactoring efforts (e.g., as outlined in [`readme/tasks/module_refactoring_plan_delegation.md`](readme/tasks/module_refactoring_plan_delegation.md:1)). Assumes `src/config.py` exists and is correctly populated.*

## 4. Core Data Flow Diagram

```mermaid
graph TD
    A[VIN List] --> LAH[Phase 1: src/last_active_handler.py];
    LAH -- Last Active Timestamp & Basic Info --> ORCH;
    ORCH[Orchestrator Logic (e.g., in async_simple.py)];
    LAH -- Last Active Data --> FH;
    ORCH -- VIN, Last Active Timestamp (converted to ms) --> LADH[Phase 2: src/last_available_data_handler.py];
    LADH -- Last Available Versions & Location (raw timestamps) --> FH;
    FH[Phase 3: src/format_handler.py];
    FH -- Combined & Formatted Row Data (timestamps to ISO) --> GSH[Phase 3: src/gsheet_handler.py];
    GSH --> OUT[Single Google Sheet Report];

    subgraph LAH_Sub [src/last_active_handler.py]
        direction LR
        LAH_Fetch[fetch_last_active_details inspired by module.py:218-225]
    end
    LAH --> LAH_Fetch;

    subgraph LADH_Sub [src/last_available_data_handler.py]
        direction LR
        LADH_Ver[fetch_vehicle_versions inspired by module.py:211-217]
        LADH_Loc[fetch_location_details inspired by module.py:259-287]
        LADH_Stage[Staged Fallback Logic using STAGES_CONFIG from module.py:30-37]
    end
    LADH --> LADH_Ver;
    LADH --> LADH_Loc;
    LADH --> LADH_Stage;

    subgraph FH_Sub [src/format_handler.py]
        direction LR
        FH_FormatLA[format_last_active_for_report (standardize timestamp)]
        FH_FormatVer[format_versions_for_report inspired by module.py:1051-1112 (standardize timestamp)]
        FH_FormatLoc[format_location_for_report (standardize timestamp if present)]
        FH_Combine[assemble_final_report_row]
    end
    FH --> FH_FormatLA;
    FH --> FH_FormatVer;
    FH --> FH_FormatLoc;
    FH --> FH_Combine;

    subgraph GSH_Sub [src/gsheet_handler.py]
        direction LR
        GSH_Auth[authenticate_google]
        GSH_Update[update_consolidated_sheet]
    end
    GSH --> GSH_Auth;
    GSH --> GSH_Update;
```

## 5. Phase-wise Breakdown

### Phase 1: Last Active Data Acquisition (`src/last_active_handler.py`)

*   **Purpose:** To create a dedicated handler responsible for fetching a vehicle's last active timestamp and other basic information from the `getVehicleInfoBMS` endpoint.
*   **Key Components/Functions:**
    *   `async def fetch_last_active_details(session: aiohttp.ClientSession, vin: str, max_retries: int) -> Optional[Dict]:`
        *   This function will encapsulate the logic for calling the endpoint: `https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS`.
        *   It will adapt logic from [`module.py:218-225`](../../module.py:218).
        *   **Return Value:** A dictionary containing `timestamp` (e.g., '2025-06-08 22:58:20'), `pack_soc`, `scooter_state`, `time_charge_full_sc_1`, `status_code`, `message`, and `data_available` (boolean indicating if primary data was found).
*   **Dependencies:** `aiohttp`, `loguru`, `src.config`.
    *   **Configuration Import:** Configuration (e.g., API URLs, log paths) will be imported directly via `from src import config as app_config`. The script will rely on standard Python import mechanisms (e.g., `PYTHONPATH` or running as a module with `python -m src.last_active_handler`) for `src.config` to be discoverable. No fallback mock configuration will be included in the handler itself for runtime.
*   **Pytest Focus:**
    *   Mock `aiohttp.ClientSession` and API responses.
    *   Verify correct parsing of successful responses.
    *   Verify handling of API error responses and retry mechanisms (if self-contained, otherwise test the common client).
    *   Verify correct structure of the returned dictionary.
*   **Standalone Execution (`if __name__ == "__main__":`)**:
    *   Include a simple block to demonstrate fetching last active details. For meaningful testing against a live API, this block should use a realistic (though potentially anonymized or designated test) VIN. For example, `test_vin = "P53BKDCB3ECA00002"`.

### Phase 2: Last Available Data Retrieval (`src/last_available_data_handler.py`)

*   **Purpose:** To fetch detailed version information (Software, BMS, BCM, MCU) and geographical location. This handler will use the "last active timestamp" (converted to milliseconds epoch) obtained from Phase 1 as a primary reference for its queries. It will employ a staged approach (looking back in time based on `STAGES_CONFIG`) if data isn't immediately found around the last active time, aiming to find the *last known good data*.
*   **Key Components/Functions:**
    *   A helper function to convert string timestamps (like '2025-06-08 22:58:20') to milliseconds epoch.
    *   `async def fetch_last_available_versions(session: aiohttp.ClientSession, vin: str, reference_timestamp_ms: int, stages_config: list, max_retries_per_stage_fn: callable) -> Optional[Dict]:`
        *   Adapts logic from [`module.py:211-217`](../../module.py:211) for fetching version info. The fetched data is expected to contain the `ecu_version` string which will be parsed later (in `format_handler.py`) for individual BMS, BCM, MCU versions.
        *   Uses `reference_timestamp_ms` and `stages_config` (similar to [`module.py:30-37`](../../module.py:30)) to define query windows if staged fetching is needed.
        *   The `max_retries_per_stage_fn` could be a helper to get retries for a given stage.
        *   **Return Value:** A dictionary containing various version details including the raw data needed for `vehicle_software_version`, and the `ecu_version` string (for later parsing of `bms_sw`, `bcm_sw`, `mcu_sw`), and `timestamp_of_data` (raw timestamp from API).
    *   `async def fetch_last_available_location(session: aiohttp.ClientSession, vin: str, reference_timestamp_ms: int, stages_config: list, max_retries_per_stage_fn: callable) -> Optional[Dict]:`
        *   This function will first attempt to get GPS coordinates (latitude, longitude) by querying a relevant endpoint (e.g., adapting `async_fetch_gps_timestamp` logic from `module.py` if GPS data is tied to time ranges).
        *   It will then use these coordinates to perform reverse geocoding via Nominatim, adapting logic from [`module.py:259-287`](../../module.py:259).
        *   Uses `reference_timestamp_ms` and `stages_config` for staged fetching of GPS data if necessary.
        *   **Return Value:** A dictionary containing `latitude`, `longitude`, `formatted_address` (e.g., "City, State"), `timestamp_of_data` (raw timestamp from API).
*   **Dependencies:** `aiohttp`, `loguru`, `src.config`, `datetime`.
*   **Pytest Focus:**
    *   Mock API responses for version info, GPS, and Nominatim.
    *   Test the staged fetching logic: ensure it iterates through stages correctly.
    *   Verify correct parsing of version and location data.
    *   Test fallback mechanisms if initial data fetches fail.
*   **Standalone Execution (`if __name__ == "__main__":`)**:
    *   Include a simple block to demonstrate fetching versions and location for a test VIN, possibly using a mock reference timestamp and a realistic test VIN.

### Phase 3: Data Formatting & Google Sheets Update

#### `src/format_handler.py`

*   **Purpose:** To consolidate all data formatting logic. This module will take raw data dictionaries from Phase 1 and Phase 2 handlers and transform them into the final structure required for the Google Sheet report. All timestamps processed by this handler should be converted to a consistent ISO-like format (e.g., `YYYY-MM-DD HH:MM:SS`).
*   **Key Components/Functions:**
    *   A utility function `def standardize_timestamp(raw_timestamp: Any) -> str:` to convert various timestamp inputs (epoch ms, string formats) to 'YYYY-MM-DD HH:MM:SS'. This can adapt logic from [`module.py:928-940`](../../module.py:928-940).
    *   `def format_last_active_for_report(last_active_data: Optional[Dict]) -> Dict:`
        *   Extracts and formats relevant fields from Phase 1 output. Ensures `last_connected_timestamp` is standardized.
    *   `def format_versions_for_report(version_data: Optional[Dict]) -> Dict:`
        *   Adapts logic from [`module.py:1051-1112`](../../module.py:1051).
        *   Extracts `vehicle_software_version`, `bms_sw`, `bcm_sw`, `mcu_sw`. Ensures any associated timestamp is standardized.
    *   `def format_location_for_report(location_data: Optional[Dict]) -> Dict:`
        *   Extracts formatted location string. Ensures any associated timestamp is standardized.
    *   `def assemble_final_report_row(vin: str, last_active_report_data: Dict, versions_report_data: Dict, location_report_data: Dict) -> List:`
        *   Combines formatted data from the above functions into a single list representing a row in the Google Sheet.
        *   **Target Columns:** `VIN`, `Location`, `Software Version`, `BMS Version`, `BCM Version`, `MCU Version`, `Last Connected`. (Note: "Model" and "Variant" removed as per user clarification).
        *   Handles cases where some data might still be missing, aiming for clarity (e.g., empty string vs. "N/A" to be decided, but goal is to minimize actual "N/A" by using last available data).
*   **Dependencies:** `src.config` (potentially), `datetime`.
*   **Pytest Focus:**
    *   Test `standardize_timestamp` with various inputs.
    *   Test each formatting function with various inputs (valid data, missing data, None).
    *   Verify the `assemble_final_report_row` function correctly constructs the row in the specified order and handles missing pieces gracefully.
*   **Standalone Execution (`if __name__ == "__main__":`)**:
    *   Include a simple block to demonstrate formatting sample raw data and assembling a report row.

#### `src/gsheet_handler.py`

*   **Purpose:** To manage all interactions with Google Sheets, including authentication and updating the single, consolidated vehicle data report.
*   **Key Components/Functions:**
    *   `def authenticate_google_services() -> Any:` (or similar, to get authenticated service object).
        *   Adapts logic from [`module.py:636-655`](../../module.py:636).
    *   `def update_consolidated_vehicle_report(service: Any, spreadsheet_id: str, sheet_name: str, report_data: List[List]) -> None:`
        *   Takes the list of formatted rows (from `format_handler.py`).
        *   Clears the existing sheet (or updates rows intelligently).
        *   Writes the new header and data to the specified single sheet.
        *   This function will embody the "tracking limited to 1 sheet" requirement.
*   **Dependencies:** `google-api-python-client`, `google-auth-httplib2`, `google-auth-oauthlib`, `src.config`.
*   **Pytest Focus:**
    *   Mock Google API service calls.
    *   Verify that `update_consolidated_vehicle_report` constructs the correct API calls to update the sheet.
    *   Test handling of existing sheet data (e.g., clearing before writing).
*   **Standalone Execution (`if __name__ == "__main__":`)**:
    *   Include a simple block to demonstrate authentication (if possible without user interaction or with mock credentials) and updating a test sheet with sample data.

## 6. Orchestration (High-Level)

A main script (e.g., `async_simple.py` or a refactored `runner.py`) will orchestrate the workflow:
1.  Initialize handlers.
2.  For each VIN:
    a.  Call `last_active_handler.fetch_last_active_details()` to get initial data and `last_active_timestamp_str`.
    b.  Convert `last_active_timestamp_str` to milliseconds epoch for `reference_timestamp_ms`.
    c.  Call `last_available_data_handler.fetch_last_available_versions()` and `fetch_last_available_location()`, passing `reference_timestamp_ms`.
    d.  Use `format_handler.assemble_final_report_row()` to create the final data row (this step includes timestamp standardization).
3.  Collect all processed rows.
4.  Call `gsheet_handler.update_consolidated_vehicle_report()` to push data to Google Sheets.

## 7. Key Data Points for Final Report (Target Google Sheet Columns)

1.  `VIN`
2.  `Location` (Formatted address, e.g., "City, State")
3.  `Software Version` (Overall vehicle software version)
4.  `BMS Version`
5.  `BCM Version`
6.  `MCU Version`
7.  `Last Connected` (Timestamp from `getVehicleInfoBMS`, standardized to ISO-like format)

*(Note: "Model" and "Variant" columns are excluded from this system's responsibility as they will be handled via post-processing from a Google Sheet, per user clarification.)*

## 8. Assumptions & Clarifications Needed

*   **Source of "Model" and "Variant":** Clarified by user. These will be handled in post-processing from a Google Sheet and are out of scope for this plan's implementation regarding API data fetching and initial formatting.
*   **Configuration Management:** API URLs, `STAGES_CONFIG`, GSheet IDs, etc., will be managed in a central `src/config.py` file, which these new handlers will import directly. If `src.config` cannot be imported, the script should fail with an `ImportError`.
*   **Error Propagation:** Handlers should return `None` or specific error indicators if data cannot be fetched, allowing the orchestrator and `format_handler` to manage this gracefully.
*   **Existing `module.py`:** The new handlers will adapt logic from `module.py` but will be new, separate files. The existing `module.py` might be refactored or parts of it deprecated over time.

## 9. Next Steps

1.  Review and approve this updated plan.
2.  Proceed with implementation, phase by phase.
3.  After implementation, switch to "Code" mode to write the code for these new modules.