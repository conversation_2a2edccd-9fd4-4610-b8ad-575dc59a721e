import asyncio
import time
from datetime import datetime, timedelta
import aiohttp
from loguru import logger
import os

# Assuming module.py is in the same directory or accessible in PYTHONPATH
from module import <PERSON><PERSON><PERSON>tamp<PERSON><PERSON><PERSON>

# --- Configuration ---
# Users can modify this list to specify which secondary features to fetch.
# Ensure these names correspond to methods in NetworkTimestampChecker (e.g., 'gps' -> 'async_fetch_gps_timestamp')
ENABLED_SECONDARY_FEATURES = [
    'connectivity',
    'gps',
    'version_info',
    # 'rsrp',
    # 'horizontal_accuracy',
    'network_timestamp'
]
MAX_RETRIES = 5  # Max retries for initial last_active_info fetch and for individual secondary features.

# Logging Setup
logs_dir_simple = os.path.join(os.getcwd(), 'logs', 'async_simple')
os.makedirs(logs_dir_simple, exist_ok=True)
logger.remove() # Remove default handler
# Ensure logs are written by explicitly enabling the logger for the current module/script context
logger.enable("__main__") # Or use the specific module name if it were imported differently
logger.add(os.path.join(logs_dir_simple, 'info.log'), level='INFO', rotation='10 MB', retention='10 days', format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}")
logger.add(os.path.join(logs_dir_simple, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days', format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}")
logger.add(lambda _: exit(1), level="CRITICAL") # Exit on critical errors

async def main():
    logger.info("async_simple.py script started and logger initialized.")
    logger.info("Starting Smart Vehicle Data Fetching Process (async_simple.py)...")
    
    checker = NetworkTimestampChecker()

    # Set feature flags in the checker based on ENABLED_SECONDARY_FEATURES
    # This ensures that only desired secondary features are processed by internal checker logic if used.
    feature_flags_to_set = {feature: False for feature in checker.feature_flags} # Disable all first
    for feature_name in ENABLED_SECONDARY_FEATURES:
        if feature_name in feature_flags_to_set:
            feature_flags_to_set[feature_name] = True
        else:
            logger.warning(f"Feature '{feature_name}' in ENABLED_SECONDARY_FEATURES is not a known feature in NetworkTimestampChecker.feature_flags. It will be ignored for checker's internal flags.")
    # Also ensure 'last_active_info' is not accidentally part of the secondary features for flag setting
    if 'last_active_info' in feature_flags_to_set:
        del feature_flags_to_set['last_active_info'] # Not a standard secondary feature flag
    
    checker.set_feature_flags(feature_flags_to_set)
    logger.info(f"Checker feature flags set to: {checker.feature_flags}")


    vin_numbers = checker.get_vin_numbers()
    if not vin_numbers:
        logger.error("No VIN numbers found. Exiting.")
        return

    logger.info(f"Found {len(vin_numbers)} VINs to process.")
    # logger.debug(f"VINs: {vin_numbers}") # Be cautious with logging all VINs if the list is very large

    aggregated_results = {}
    # The 'last_active_info' key will store results from getVehicleInfoBMS
    # Other keys will correspond to ENABLED_SECONDARY_FEATURES
    
    current_script_time_ms = int(time.time() * 1000) # For fallback time calculations

    async with aiohttp.ClientSession() as session:
        for i, vin in enumerate(vin_numbers):
            logger.info(f"Processing VIN {i+1}/{len(vin_numbers)}: {vin}")
            aggregated_results.setdefault('last_active_info', {})
            
            # 1. Fetch Last Active Info
            last_active_data = None
            try:
                last_active_data = await checker.async_fetch_last_active_info(session, vin, MAX_RETRIES)
                if last_active_data:
                    logger.info(f"Successfully fetched last active info for VIN {vin}.")
                    # logger.debug(f"VIN {vin} - Last Active Data: {last_active_data}")
                else:
                    logger.warning(f"Failed to fetch last active info for VIN {vin} after {MAX_RETRIES} retries or data was empty.")
                    # Store a record of failure for this primary step
                    last_active_data = { # Ensure a structure for sheet formatting
                        "status_code": "N/A", 
                        "message": "Failed to fetch or parse last active info", 
                        "data_available": "No"
                    }
            except Exception as e:
                logger.error(f"Exception during async_fetch_last_active_info for VIN {vin}: {e}")
                last_active_data = { # Ensure a structure for sheet formatting
                    "status_code": "Exception", 
                    "message": str(e), 
                    "data_available": "No"
                }
            
            aggregated_results['last_active_info'][vin] = last_active_data

            # 2. Determine Query Strategy for Enabled Secondary Features
            vehicle_last_active_ms = None
            if last_active_data and last_active_data.get("data_available") == "Yes" and last_active_data.get('timestamp'):
                try:
                    # Example timestamp: "2025-04-17 11:03:34"
                    dt_obj = datetime.strptime(last_active_data['timestamp'], "%Y-%m-%d %H:%M:%S")
                    # Assuming the timestamp is in local time and needs to be treated as such for epoch conversion.
                    # If it's UTC, ensure datetime.utcfromtimestamp or similar is used if converting back.
                    vehicle_last_active_ms = int(dt_obj.timestamp() * 1000)
                    logger.info(f"VIN {vin}: Last active timestamp '{last_active_data['timestamp']}' converted to epoch ms: {vehicle_last_active_ms}.")
                    logger.debug(f"VIN {vin}: vehicle_last_active_ms = {vehicle_last_active_ms}")
                except ValueError as ve:
                    logger.error(f"VIN {vin}: Could not parse timestamp '{last_active_data['timestamp']}'. Error: {ve}. Skipping targeted secondary feature fetch for this VIN.")
                    vehicle_last_active_ms = None

            if vehicle_last_active_ms is not None:
                logger.info(f"VIN {vin}: Using targeted 24-hour query based on last active time.")
                query_to_time_ms = vehicle_last_active_ms
                query_from_time_ms = vehicle_last_active_ms - (24 * 60 * 60 * 1000) # 24 hours prior
                
                from_time_hr = datetime.fromtimestamp(query_from_time_ms/1000).strftime('%Y-%m-%d %H:%M:%S')
                to_time_hr = datetime.fromtimestamp(query_to_time_ms/1000).strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"VIN {vin}: Targeted 24-hour query window for secondary features: FROM {from_time_hr} (epoch: {query_from_time_ms}) TO {to_time_hr} (epoch: {query_to_time_ms}).")

                for feature_name in ENABLED_SECONDARY_FEATURES:
                    if not checker.feature_flags.get(feature_name):
                        # logger.debug(f"VIN {vin}: Skipping secondary feature '{feature_name}' as it's not enabled in checker flags.")
                        continue
                    
                    fetch_method_name = f"async_fetch_{feature_name}"
                    if hasattr(checker, fetch_method_name):
                        fetch_method = getattr(checker, fetch_method_name)
                        feature_data = None
                        try:
                            # Check if method accepts from_time and to_time (crude check based on common pattern)
                            # A more robust way would be inspect.signature if this becomes complex
                            if "from_time" in fetch_method.__code__.co_varnames and "to_time" in fetch_method.__code__.co_varnames:
                                logger.debug(f"VIN {vin}: Calling {fetch_method_name} for feature '{feature_name}' with time window: from_time={query_from_time_ms} ({from_time_hr}), to_time={query_to_time_ms} ({to_time_hr}).")
                                feature_data = await fetch_method(session, vin, query_from_time_ms, query_to_time_ms, MAX_RETRIES)
                            else:
                                logger.debug(f"VIN {vin}: Calling {fetch_method_name} for feature '{feature_name}' without time window.")
                                feature_data = await fetch_method(session, vin, MAX_RETRIES)
                            
                            if feature_data:
                                logger.info(f"VIN {vin}: Successfully fetched data for '{feature_name}'.")
                            else:
                                logger.warning(f"VIN {vin}: No data returned for '{feature_name}' in targeted query.")
                        except Exception as e:
                            logger.error(f"VIN {vin}: Exception fetching '{feature_name}' in targeted query: {e}")
                            feature_data = {"error": str(e), "status_code": "Exception"} # Store error
                        
                        aggregated_results.setdefault(feature_name, {})[vin] = feature_data
                    else:
                        logger.warning(f"VIN {vin}: Fetch method '{fetch_method_name}' not found in NetworkTimestampChecker for feature '{feature_name}'.")
            else:
                # If vehicle_last_active_ms is None, it means we couldn't get a valid last active time.
                # Per revised plan, we skip secondary features for this VIN.
                logger.warning(f"VIN {vin}: Last active data was invalid or fetch failed. Skipping all secondary feature fetches for this VIN as fallback logic is removed.")
                # Optionally, ensure placeholder entries for skipped secondary features if strict sheet structure is needed
                for feature_name in ENABLED_SECONDARY_FEATURES:
                    if checker.feature_flags.get(feature_name):
                        aggregated_results.setdefault(feature_name, {})[vin] = {"error": "Skipped; Last active time unavailable", "status_code": "Skipped"}

    # Finalization
    logger.info("All VINs processed. Finalizing data...")
    try:
        checker.save_pickle_data(aggregated_results)
        logger.info(f"Aggregated results saved to '{checker.pickle_file}'.")
    except Exception as e:
        logger.error(f"Error saving pickle data: {e}")

    try:
        # Ensure master_sheet_id is available in checker, or pass it if it's configured elsewhere
        # For this script, we assume checker.master_sheet_id is set or default.
        if not checker.master_sheet_id:
             logger.warning("Master sheet ID not set in checker. Google Sheets update might fail or use a default.")
        
        # The update_or_create_result_google_sheet method in module.py now expects 'last_active_info'
        # as a top-level key in aggregated_results, similar to other features.
        spreadsheet_id = checker.update_or_create_result_google_sheet(aggregated_results, checker.master_sheet_id)
        if spreadsheet_id:
            logger.info(f"Google Sheet updated/created successfully. ID: {spreadsheet_id}")
            logger.info(f"Spreadsheet URL: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
        else:
            logger.error("Failed to update or create Google Sheet.")
            
    except Exception as e:
        logger.error(f"Error updating/creating Google Sheet: {e}")

    logger.info("Smart Vehicle Data Fetching Process (async_simple.py) finished.")


if __name__ == "__main__":
    # To handle potential asyncio event loop issues on Windows when run from certain environments
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())