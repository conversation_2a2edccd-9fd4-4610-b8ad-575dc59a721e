./test_runner.ps1
Executing last_active_handler.py. Current CWD: C:\Users\<USER>\olae_enterprise_git\telematics_command_center_nw_checker
2025-06-08 22:58:25.315 | INFO     | __main__:<module>:23 - <PERSON><PERSON> configured. CWD: C:\Users\<USER>\olae_enterprise_git\telematics_command_center_nw_checker. Log directory: C:\Users\<USER>\olae_enterprise_git\telematics_command_center_nw_checker\logs\last_active_handler
2025-06-08 22:58:25.317 | INFO     | __main__:main:182 - Starting standalone test for last_active_handler with VIN: P53BKDCB3ECA00002
2025-06-08 22:58:25.317 | INFO     | __main__:fetch_last_active_details:129 - Fetching last active details for VIN: P53BKDCB3ECA00002 from https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS?vehicleId=P53BKDCB3ECA00002 with max 2 retries.
2025-06-08 22:58:25.318 | DEBUG    | __main__:_async_fetch_with_retry:75 - Attempting API call
2025-06-08 22:58:25.478 | INFO     | __main__:_async_fetch_with_retry:85 - API call successful
2025-06-08 22:58:25.479 | INFO     | __main__:main:191 - Fetched details for P53BKDCB3ECA00002: {'timestamp': '2025-06-08 22:58:20', 'pack_soc': '92.0', 'scooter_state': 'UNLOCK', 'time_charge_full_sc_1': '70.0', 'status_code': 200, 'message': 'Success', 'data_available': 'Yes'}
2025-06-08 22:58:25.479 | INFO     | __main__:main:195 - Standalone test finished.