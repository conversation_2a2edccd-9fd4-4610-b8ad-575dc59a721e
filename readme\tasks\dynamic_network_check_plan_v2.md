# Plan to Enhance Network Status Checking Efficiency - V2

**Objective:**
Refactor `module.py` to implement a staged, dynamic approach for checking network status. This version ensures that if an enabled feature fails for a VIN in one stage, it is retried for that specific VIN/feature combination in subsequent stages. Additionally, it incorporates an `asyncio.Semaphore` to throttle concurrent network requests. The cron job frequency in `runner.py` remains at every three hours.

**High-Level Design:**

The core idea is to track pending work on a per-VIN, per-feature basis. Each stage will attempt to fetch data for these pending (VIN, feature) pairs.

```mermaid
graph TD
    A[Start: Get All VINs & Enabled Features] --> B[Initialize pending_vin_features: Map VIN -> Set of pending features];
    B --> C{Loop Through Stages defined in STAGES_CONFIG};
    C -- Next Stage --> D[Identify (VIN, Feature) pairs still in pending_vin_features];
    D -- Any pending? --> E{Create Guarded Fetch Tasks for pending (VIN, Feature) pairs using Semaphore};
    E --> F[Execute Tasks via asyncio.gather];
    F --> G{Process Results for each (VIN, Feature, Data)};
    G -- Hit (Data Not None) --> H[Store Data in aggregated_results, Remove Feature from pending_vin_features[VIN]];
    G -- Miss (Data is None) --> I[Feature remains in pending_vin_features[VIN] for next stage];
    H --> J{Any (VIN,Feature) pairs still pending globally?};
    I --> J;
    J -- Yes --> C;
    J -- No --> K[All (VIN,Feature) pairs processed or all stages done];
    K --> L[Save Aggregated Results (Pickle, Google Sheets)];
    L --> M[End];

    subgraph Stages
        direction LR
        S1[Stage 1: 1 Hr, 2 Retries]
        S2[Stage 2: 6 Hrs, 3 Retries]
        S3[Stage 3: 24 Hrs, 4 Retries]
        S4[Stage 4: 3 Days, 5 Retries]
        S5[Stage 5: 1 Month, 6 Retries]
        S6[Stage 6: 3 Months, 7 Retries]
    end
    C --> S1 --> S2 --> S3 --> S4 --> S5 --> S6;
```

**Detailed Changes for `module.py`:**

1.  **`NetworkTimestampChecker` Class:**
    *   Add an `asyncio.Semaphore` instance, possibly in `__init__` or as a class attribute, e.g., `self.fetch_semaphore = asyncio.Semaphore(200)`. The limit (e.g., 200) should be configurable or tuned.

2.  **Refactor `run_async()` Method:**
    *   **Initialization:**
        *   Get `vin_numbers`.
        *   Get `enabled_features_list` from `self.feature_flags`.
        *   Initialize `pending_vin_features = {vin: set(enabled_features_list) for vin in vin_numbers}`. This dictionary will track which features still need to be successfully fetched for each VIN.
        *   Initialize `aggregated_results = {feature: {} for feature in enabled_features_list}`. This will store the *first successful result* for each (VIN, feature) pair.
        *   `current_to_time = int(time.time() * 1000)`.
    *   **Stage Loop:**
        *   Iterate through `self.STAGES_CONFIG`.
        *   At the start of each stage, determine `tasks_for_this_stage`. This will be a list of (VIN, feature_name) tuples for which `feature_name` is still in `pending_vin_features[VIN]`.
        *   If `tasks_for_this_stage` is empty, `break` the loop (all work is done).
        *   Calculate `stage_from_time` and `retries_for_stage` from the current stage config.
        *   Call a (possibly refactored or new) batch fetching method (e.g., `execute_stage_fetches`) with `tasks_for_this_stage`, `stage_from_time`, `current_to_time`, and `retries_for_stage`.
    *   **Processing Results from `execute_stage_fetches`:**
        *   The batch fetching method should return results mapped to the (VIN, feature_name) tasks.
        *   For each `(vin, feature_name, data)` result:
            *   If `data is not None` (a successful fetch for this specific feature):
                *   If `vin` is not already in `aggregated_results[feature_name]`, store it: `aggregated_results[feature_name][vin] = data`. (This ensures we only keep the first successful fetch, which corresponds to the shortest time window).
                *   Remove `feature_name` from `pending_vin_features[vin]`.
    *   **Finalization:**
        *   After the loop, save `aggregated_results` to pickle and Google Sheets.

3.  **Refactor `async_batch_fetch_all()` (or new `execute_stage_fetches()`):**
    *   This method will now accept a list of specific (VIN, feature_name) tasks to execute for the current stage, along with `from_time`, `to_time`, and `max_retries_for_stage`.
    *   **Semaphore Usage:**
        *   Define a helper async function `_guarded_fetch(coro)`:
          ```python
          async def _guarded_fetch(coro):
              async with self.fetch_semaphore: # Assumes semaphore is self.fetch_semaphore
                  return await coro
          ```
    *   Iterate through the input (VIN, feature_name) tasks.
    *   For each task, select the appropriate `self.async_fetch_<feature_name>()` method.
    *   Create the coroutine call, e.g., `self.async_fetch_connectivity_data(session, vin, from_time, to_time, max_retries_for_stage)`.
    *   Wrap this coroutine with `_guarded_fetch`: `_guarded_fetch(coroutine_call)`.
    *   Collect all these guarded tasks.
    *   Use `asyncio.gather(*guarded_tasks, return_exceptions=True)` to execute them.
    *   Return the results in a way that they can be mapped back to the originating (VIN, feature_name) task.

4.  **No Changes to `async_fetch_<datatype>()` methods' signatures:**
    *   These methods (`async_fetch_network_timestamp`, `async_fetch_connectivity_data`, etc.) already accept `max_retries_for_stage` and other necessary parameters. Their internal logic remains the same.

**No Changes to `runner.py`:**
The cron schedule update in `runner.py` is assumed to be correct from the previous iteration.

**Summary of Key Logic Changes from V1 Plan:**
*   The primary state tracking shifts from a simple list of `pending_vins` to `pending_vin_features`, a dictionary mapping each VIN to a set of its *still-pending features*.
*   `async_batch_fetch_all` (or its replacement) becomes more targeted, only creating fetch tasks for (VIN, feature) pairs that are explicitly still pending for the current stage.
*   A successful fetch for a (VIN, feature) pair removes that specific feature from the VIN's pending set. The VIN itself might still have other features pending.
*   The `asyncio.Semaphore` is introduced in the batch fetching logic to throttle concurrent network requests.