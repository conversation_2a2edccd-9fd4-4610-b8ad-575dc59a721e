import pandas as pd
import requests
import time
from datetime import datetime
import json
import pickle
import os
import pathlib
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
import threading
import asyncio
import aiohttp
import portalocker
from typing import Dict, Any, Optional
from loguru import logger

# Logging Setup (loguru only)
logs_dir = os.path.join(os.getcwd(), 'logs', 'module')
os.makedirs(logs_dir, exist_ok=True)
logger.remove()
logger.add(os.path.join(logs_dir, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(logs_dir, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

class NetworkTimestampChecker:
    # Scope for accessing Google Drive files
    SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
    
    STAGES_CONFIG = [
        {"name": "1_hour", "duration_spec": {"hours": 1}, "retries": 2},
        {"name": "6_hours", "duration_spec": {"hours": 6}, "retries": 3},
        {"name": "24_hours", "duration_spec": {"hours": 24}, "retries": 4},
        {"name": "3_days", "duration_spec": {"days": 3}, "retries": 5},
        {"name": "1_month", "duration_spec": {"days": 30}, "retries": 6}, # As per clarification
        {"name": "3_months", "duration_spec": {"days": 90}, "retries": 7}, # As per clarification
    ]
    
    def __init__(self):
        self.cwd = os.getcwd()
        self.input_path = os.path.join(self.cwd, 'input')
        self.results = []
        self.requests_data = {}
        self.pickle_file = 'request_results.pickle'
        self.lock = threading.Lock()
        self.timeout = aiohttp.ClientTimeout(total=17)  # 2 seconds timeout for initial requests
        self.retry_timeout = aiohttp.ClientTimeout(total=20)  # 20 seconds timeout for retry requests
        self.fetch_semaphore = asyncio.Semaphore(200)  # Semaphore to throttle concurrent requests
        # Create results directory if it doesn't exist
        self.results_dir = os.path.join(self.cwd, 'results')
        os.makedirs(self.results_dir, exist_ok=True)
        self.request_log_file = os.path.join(self.results_dir, f'request_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        self.master_sheet_id = '1bEA2XtFGZEXgCBjkDqWgjBohCat02AAj6rCq-3QMuGI'
        self.master_vin_list_sheet_name = 'VIN_ICCID'
        # Default feature flags - developers can modify these flags to control which features are enabled
        self.feature_flags = {
            'network_timestamp': True,
            'connectivity': True,
            'gps': True,
            'rsrp': True,
            'horizontal_accuracy': True,
            'version_info': True
        }

    def load_pickle_data(self) -> Dict[str, Any]:
        """Thread-safe loading of pickle data"""
        try:
            with self.lock:
                if os.path.exists(self.pickle_file):
                    with portalocker.Lock(self.pickle_file, 'rb', timeout=60) as f:
                        try:
                            return pickle.load(f)
                        except Exception as e:
                            print(f"Error loading pickle data: {str(e)}")
                            return {}
                return {}
        except Exception as e:
            print(f"Error accessing pickle file: {str(e)}")
            return {}

    def save_pickle_data(self, data: Dict[str, Any]):
        """Thread-safe saving of pickle data"""
        try:
            with self.lock:
                with portalocker.Lock(self.pickle_file, 'wb', timeout=60) as f:
                    try:
                        pickle.dump(data, f)
                    except Exception as e:
                        print(f"Error saving pickle data: {str(e)}")
        except Exception as e:
            print(f"Error accessing pickle file: {str(e)}")

    def update_pickle_data(self, vin: str, data: Dict[str, Any]):
        """Thread-safe update of specific VIN data"""
        current_data = self.load_pickle_data()
        current_data[vin] = data
        self.save_pickle_data(current_data)

    async def log_request(self, url: str, response_data: Any, vin: str, data_type: str, status_code: int):
        """Log request details to file"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'vin': vin,
            'data_type': data_type,
            'url': url,
            'status_code': status_code,
            'response': response_data
        }
        
        try:
            with self.lock:
                # Load existing logs if file exists
                existing_logs = []
                if os.path.exists(self.request_log_file):
                    with open(self.request_log_file, 'r') as f:
                        existing_logs = json.load(f)
                
                # Append new log
                existing_logs.append(log_entry)
                
                # Save updated logs
                with open(self.request_log_file, 'w') as f:
                    json.dump(existing_logs, f, indent=2)
        except Exception as e:
            print(f"Error logging request: {str(e)}")

    async def async_fetch_with_retry(self, session: aiohttp.ClientSession, url: str, vin: str, data_type: str, max_retries_for_stage: int, is_retry: bool = False, retry_count: int = 0) -> Optional[Dict]:
        """Async fetch with retry mechanism"""
        if retry_count >= max_retries_for_stage:
            print(f"Maximum retry attempts ({max_retries_for_stage}) reached for VIN {vin} for {data_type}")
            await self.log_request(url, f"Max retries ({max_retries_for_stage}) exceeded", vin, data_type, 429)
            return None
            
        timeout = self.retry_timeout if is_retry else self.timeout
        try:
            async with session.get(url, timeout=timeout) as response:
                status_code = response.status
                if status_code == 200:
                    data = await response.json()
                    await self.log_request(url, data, vin, data_type, status_code)
                    self.update_pickle_data(vin, {
                        'data': data,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'success',
                        'retry_count': retry_count
                    })
                    return data
                elif status_code == 204:
                    print(f"No content available for VIN {vin} for {data_type}")
                    await self.log_request(url, "No Content", vin, data_type, status_code)
                    return None
                else:
                    print(f"Error fetching data for VIN {vin} ({data_type}): Status code {status_code} (Attempt {retry_count + 1}/{max_retries_for_stage})")
                    await self.log_request(url, None, vin, data_type, status_code)
                    if status_code == 408 or isinstance(response, asyncio.TimeoutError): # Check if response is an instance of TimeoutError
                        return await self.schedule_retry(session, url, vin, data_type, max_retries_for_stage, retry_count + 1)
                    return None
        except asyncio.TimeoutError:
            print(f"Timeout for VIN {vin} ({data_type}) (Attempt {retry_count + 1}/{max_retries_for_stage})")
            await self.log_request(url, "Timeout Error", vin, data_type, 408)
            return await self.schedule_retry(session, url, vin, data_type, max_retries_for_stage, retry_count + 1)
        except Exception as e:
            print(f"Exception while fetching data for VIN {vin} ({data_type}): {str(e)} (Attempt {retry_count + 1}/{max_retries_for_stage})")
            await self.log_request(url, str(e), vin, data_type, 500)
            # Consider specific exceptions that warrant a retry
            if "Cannot connect" in str(e) or "Connection reset" in str(e) or "Connection refused" in str(e):
                return await self.schedule_retry(session, url, vin, data_type, max_retries_for_stage, retry_count + 1)
            return None

    async def schedule_retry(self, session: aiohttp.ClientSession, url: str, vin: str, data_type: str, max_retries_for_stage: int, retry_count: int) -> Optional[Dict]:
        """Schedule a retry attempt with increasing delay"""
        print(f"Scheduling retry #{retry_count}/{max_retries_for_stage} for VIN {vin} ({data_type})")
        await asyncio.sleep(15)  # Fixed 15 second retry interval
        return await self.async_fetch_with_retry(session, url, vin, data_type, max_retries_for_stage, is_retry=True, retry_count=retry_count)

    async def async_fetch_network_timestamp(self, session: aiohttp.ClientSession, vin: str, from_time: int, to_time: int, max_retries_for_stage: int) -> Optional[Dict]:
        """Async version of fetch_network_timestamp"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/networktimestamp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        return await self.async_fetch_with_retry(session, url, vin, "network_timestamp", max_retries_for_stage)

    async def async_fetch_connectivity_data(self, session: aiohttp.ClientSession, vin: str, from_time: int, to_time: int, max_retries_for_stage: int) -> Optional[Dict]:
        """Async version of fetch_connectivity_data"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getNetworkData"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        return await self.async_fetch_with_retry(session, url, vin, "connectivity", max_retries_for_stage)

    async def async_fetch_gps_timestamp(self, session: aiohttp.ClientSession, vin: str, from_time: int, to_time: int, max_retries_for_stage: int) -> Optional[Dict]:
        """Async version of fetch_gps_timestamp"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        return await self.async_fetch_with_retry(session, url, vin, "gps", max_retries_for_stage)

    async def async_fetch_rsrp(self, session: aiohttp.ClientSession, vin: str, from_time: int, to_time: int, max_retries_for_stage: int, percentile: int = 75) -> Optional[Dict]:
        """Async version of fetch_rsrp"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/rsrp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&percentile={percentile}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, percentile={percentile}]")
        return await self.async_fetch_with_retry(session, url, vin, "rsrp", max_retries_for_stage)

    async def async_fetch_horizontal_accuracy(self, session: aiohttp.ClientSession, vin: str, from_time: int, to_time: int, max_retries_for_stage: int, percentile: int = 50) -> Optional[Dict]:
        """Async version of fetch_horizontal_accuracy"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/horizontalaccuracy"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&percentile={percentile}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, percentile={percentile}]")
        return await self.async_fetch_with_retry(session, url, vin, "horizontal_accuracy", max_retries_for_stage)

    async def async_fetch_version_info(self, session: aiohttp.ClientSession, vin: str, max_retries_for_stage: int) -> Optional[Dict]:
        """Async version of fetch_version_info"""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/vehicle/versioninfo"
        url = f"{base_url}?vin={vin}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}]")
        return await self.async_fetch_with_retry(session, url, vin, "version_info", max_retries_for_stage)

    async def async_fetch_last_active_info(self, session: aiohttp.ClientSession, vin: str, max_retries_for_stage: int) -> Optional[Dict]:
        """Fetch basic vehicle information, primarily the last active timestamp."""
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getVehicleInfoBMS"
        url = f"{base_url}?vehicleId={vin}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}]")
        # This endpoint does not require authentication as per the plan
        response_data = await self.async_fetch_with_retry(session, url, vin, "last_active_info", max_retries_for_stage)
        
        if response_data and response_data.get('statusCode') == 200 and response_data.get('data'):
            # Plan specifies data is in data[0]
            vehicle_info_list = response_data['data']
            vehicle_info = vehicle_info_list[0] if isinstance(vehicle_info_list, list) and len(vehicle_info_list) > 0 else {}
            if vehicle_info: # Check if vehicle_info (i.e. data[0]) is not empty
                return {
                    "timestamp": vehicle_info.get("timestamp"), # This is the vehicle's last active timestamp
                    "pack_soc": vehicle_info.get("pack_soc"),
                    "scooter_state": vehicle_info.get("scooter_state"),
                    "time_charge_full_sc_1": vehicle_info.get("time_charge_full_sc_1"),
                    "status_code": response_data.get('statusCode'), # Use actual statusCode from response
                    "message": response_data.get('message'),         # Use actual message from response
                    "data_available": "Yes"
                }
            else: # Handle case where data array is empty or data[0] is empty
                return {
                    "status_code": response_data.get('statusCode'), # Use actual statusCode
                    "message": "Data array in response was empty or malformed.",
                    "data_available": "No"
                }
        elif response_data: # Handle cases where API returned a non-200 statusCode or other issues
             return {
                "status_code": response_data.get('statusCode', 500), # Default to 500 if not present in error response
                "message": response_data.get('message', "Failed to fetch or parse data"),
                "data_available": "No"
            }
        # This case handles if async_fetch_with_retry itself returned None (e.g., max retries exceeded, network error)
        return {
            "status_code": "N/A", # Or a specific internal error code
            "message": "Network/Retry error before API response",
            "data_available": "No"
        }

    async def get_location_name(self, latitude: str, longitude: str) -> str:
        """Get location name from coordinates using Nominatim API"""
        if not latitude or not longitude:
            return ''
            
        try:
            url = f"https://nominatim.openstreetmap.org/reverse?lat={latitude}&lon={longitude}&format=json"
            headers = {
                'User-Agent': 'OlaNetworkTimestampChecker/1.0'
            }
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        address = data.get('address', {})
                        # Extract relevant address components
                        zone = address.get('suburb', '') or address.get('neighbourhood', '')
                        city = address.get('city', '') or address.get('town', '') or address.get('village', '')
                        state = address.get('state', '')
                        
                        # Combine components that are present
                        location_parts = [part for part in [zone, city, state] if part]
                        return ', '.join(location_parts) if location_parts else ''
                    else:
                        print(f"Error getting location: Status code {response.status}")
                        return ''
        except Exception as e:
            print(f"Error getting location name: {str(e)}")
            return ''

    async def async_batch_fetch_all(self, vin_numbers: list, from_time: int, to_time: int, max_retries_for_stage: int):
        """Process multiple VINs and multiple data types concurrently based on feature flags"""
        all_tasks = []
        # Initialize results dict only for enabled features
        all_results = {
            feature: {} for feature, enabled in self.feature_flags.items() if enabled
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            for vin in vin_numbers:
                # Create tasks only for enabled features
                tasks = []
                if self.feature_flags.get('network_timestamp'):
                    tasks.append((vin, 'network_timestamp',
                        self.async_fetch_network_timestamp(session, vin, from_time, to_time, max_retries_for_stage)))
                if self.feature_flags.get('connectivity'):
                    tasks.append((vin, 'connectivity',
                        self.async_fetch_connectivity_data(session, vin, from_time, to_time, max_retries_for_stage)))
                if self.feature_flags.get('gps'):
                    tasks.append((vin, 'gps',
                        self.async_fetch_gps_timestamp(session, vin, from_time, to_time, max_retries_for_stage)))
                if self.feature_flags.get('rsrp'):
                    tasks.append((vin, 'rsrp',
                        self.async_fetch_rsrp(session, vin, from_time, to_time, max_retries_for_stage)))
                if self.feature_flags.get('horizontal_accuracy'):
                    tasks.append((vin, 'horizontal_accuracy',
                        self.async_fetch_horizontal_accuracy(session, vin, from_time, to_time, max_retries_for_stage)))
                if self.feature_flags.get('version_info'):
                    tasks.append((vin, 'version_info',
                        self.async_fetch_version_info(session, vin, max_retries_for_stage)))
                
                all_tasks.extend(tasks)
            
            if not all_tasks:
                logger.warning("No features enabled. Skipping data collection.")
                return all_results
            
            # Execute all tasks concurrently and wait for all to complete
            results = await asyncio.gather(*[task for _, _, task in all_tasks], return_exceptions=True)
            
            # Process results
            for (vin, data_type, _), result in zip(all_tasks, results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing {data_type} for VIN {vin}: {str(result)}")
                    continue
                
                if result:
                    # If it's GPS data and GPS flag is enabled, get location name
                    if data_type == 'gps' and self.feature_flags.get('gps') and result.get('data'):
                        gps_data = result['data'][0] if result['data'] else {}
                        try:
                            location = await self.get_location_name(
                                gps_data.get('latitude', ''),
                                gps_data.get('longitude', '')
                            )
                            # Store location in the result
                            if gps_data:
                                gps_data['location'] = location
                                result['data'][0] = gps_data
                        except Exception as e:
                            logger.error(f"Error getting location for VIN {vin}: {str(e)}")
                            if gps_data:
                                gps_data['location'] = ''
                                result['data'][0] = gps_data
                    
                    all_results[data_type][vin] = result
                else:
                    logger.warning(f"No valid data for {data_type} VIN {vin}")
                    all_results[data_type][vin] = None

        return all_results
    
    async def async_batch_fetch_all_v2(self, tasks_for_this_stage: list, from_time: int, to_time: int, max_retries_for_stage: int):
        """
        Process a specific list of (VIN, feature_name) tasks for a given stage,
        throttling concurrent requests using a semaphore.
        Returns a list of (vin, feature_name, data_or_exception) tuples.
        """
        
        async def _guarded_fetch(coro):
            async with self.fetch_semaphore:
                # logger.debug(f"Semaphore acquired for a task. Current semaphore value: {self.fetch_semaphore._value}")
                try:
                    return await coro
                finally:
                    # logger.debug(f"Semaphore released for a task. Current semaphore value: {self.fetch_semaphore._value +1 }") # +1 because it's after release
                    pass

        if not tasks_for_this_stage:
            logger.info("ASYNC_BATCH_FETCH_ALL_V2: No tasks provided for this stage.")
            return []

        all_coroutines_to_run = []
        # Store the original (vin, feature_name) to map results back
        task_metadata = []

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session: # Consider if session needs to be managed outside if called per stage
            for vin, feature_name in tasks_for_this_stage:
                task_metadata.append((vin, feature_name))
                coro = None
                if feature_name == 'network_timestamp':
                    coro = self.async_fetch_network_timestamp(session, vin, from_time, to_time, max_retries_for_stage)
                elif feature_name == 'connectivity':
                    coro = self.async_fetch_connectivity_data(session, vin, from_time, to_time, max_retries_for_stage)
                elif feature_name == 'gps':
                    coro = self.async_fetch_gps_timestamp(session, vin, from_time, to_time, max_retries_for_stage)
                elif feature_name == 'rsrp':
                    # Assuming default percentile if not specified, or modify to pass it if needed
                    coro = self.async_fetch_rsrp(session, vin, from_time, to_time, max_retries_for_stage)
                elif feature_name == 'horizontal_accuracy':
                    # Assuming default percentile
                    coro = self.async_fetch_horizontal_accuracy(session, vin, from_time, to_time, max_retries_for_stage)
                elif feature_name == 'version_info':
                    coro = self.async_fetch_version_info(session, vin, max_retries_for_stage)
                
                if coro:
                    all_coroutines_to_run.append(_guarded_fetch(coro))
                else:
                    logger.warning(f"ASYNC_BATCH_FETCH_ALL_V2: Unknown feature_name '{feature_name}' for VIN {vin}. Skipping.")
                    # Add a placeholder for results to maintain order if necessary, or handle missing tasks
                    all_coroutines_to_run.append(asyncio.create_task(asyncio.sleep(0, result=NotImplementedError(f"Unknown feature: {feature_name}"))))


            if not all_coroutines_to_run:
                logger.info("ASYNC_BATCH_FETCH_ALL_V2: No valid coroutines created for tasks.")
                return []
            
            logger.debug(f"ASYNC_BATCH_FETCH_ALL_V2: Executing {len(all_coroutines_to_run)} guarded tasks via asyncio.gather...")
            raw_results = await asyncio.gather(*all_coroutines_to_run, return_exceptions=True)
            logger.debug(f"ASYNC_BATCH_FETCH_ALL_V2: asyncio.gather completed. Processing {len(raw_results)} results.")

            # Combine metadata with results
            final_results = []
            for i, result_item in enumerate(raw_results):
                vin, feature_name = task_metadata[i]
                # Process GPS location if successful and data is present
                if feature_name == 'gps' and not isinstance(result_item, Exception) and result_item and result_item.get('data'):
                    gps_data_list = result_item['data']
                    if gps_data_list: # Ensure there's data
                        gps_entry = gps_data_list[0] if gps_data_list else {}
                        try:
                            location = await self.get_location_name(
                                gps_entry.get('latitude', ''),
                                gps_entry.get('longitude', '')
                            )
                            gps_entry['location'] = location
                        except Exception as e:
                            logger.error(f"ASYNC_BATCH_FETCH_ALL_V2: Error getting location for VIN {vin} (GPS): {str(e)}")
                            gps_entry['location'] = '' # Ensure location key exists
                
                final_results.append((vin, feature_name, result_item))
            
            return final_results

    
    def get_vin_numbers(self):
        """Get VIN numbers from Google Sheets"""
        sheet_values = self.get_google_sheet(self.master_sheet_id, self.master_vin_list_sheet_name)
        if not sheet_values or len(sheet_values) < 2:
            logger.error("No data found in VIN list sheet")
            return []
            
        # Get headers from first row
        headers = sheet_values[0]
        
        # Get data rows
        data_rows = sheet_values[1:]
        
        # Check if there's a column count mismatch between headers and data
        max_data_columns = max(len(row) for row in data_rows) if data_rows else 0
        
        if max_data_columns > len(headers):
            # Create arbitrary headers to match the data column count
            additional_cols = max_data_columns - len(headers)
            logger.warning(f"Column mismatch: {len(headers)} headers but data has {max_data_columns} columns. Adding arbitrary headers.")
            
            # Add arbitrary column names for the extra columns
            for i in range(additional_cols):
                headers.append(f"Column_{len(headers) + 1}")
        
        # Create DataFrame with the processed headers
        values_df = pd.DataFrame(data_rows, columns=headers, index=None)
        
        # Ensure 'VIN' column exists
        if 'VIN' not in values_df.columns:
            logger.error("No 'VIN' column found in the sheet")
            return []
            
        vin_list = values_df['VIN'].tolist()
        return vin_list

    async def run_async(self):
        """Run the async version of the checker with proper thread completion and staged, per-feature fetching."""
        try:
            logger.info("RUN_ASYNC_V2: Starting new data collection cycle with staged per-feature fetching...")
            
            vin_numbers = self.get_vin_numbers()
            if not vin_numbers:
                logger.error("RUN_ASYNC_V2: No VIN numbers found. Skipping data collection.")
                return

            enabled_features_list = [feature for feature, enabled in self.feature_flags.items() if enabled]
            if not enabled_features_list:
                logger.warning("RUN_ASYNC_V2: No features enabled. Skipping data collection.")
                return
            logger.info(f"RUN_ASYNC_V2: Enabled features: {', '.join(enabled_features_list)}")

            pending_vin_features = {vin: set(enabled_features_list) for vin in vin_numbers}
            # Log initial pending_vin_features (sample)
            sample_initial_pending = {k: list(v) for k, v in list(pending_vin_features.items())[:5]}
            logger.debug(f"RUN_ASYNC_V2: Initial pending_vin_features (sample {len(sample_initial_pending)} VINs): {sample_initial_pending}")
            
            aggregated_results = {feature: {} for feature in enabled_features_list}
            current_to_time = int(time.time() * 1000)

            logger.info(f"RUN_ASYNC_V2: Starting stage loop. Total stages: {len(self.STAGES_CONFIG)}")
            for i, stage_config in enumerate(self.STAGES_CONFIG):
                stage_name = stage_config["name"]
                logger.info(f"RUN_ASYNC_V2: ----- STAGE {i+1}/{len(self.STAGES_CONFIG)} ({stage_name}) LOOP START -----")

                # Determine (VIN, feature) tasks for this stage
                tasks_for_this_stage = []
                for vin, features_to_try in pending_vin_features.items():
                    if features_to_try: # If there are any features still pending for this VIN
                        for feature_name in features_to_try:
                            tasks_for_this_stage.append((vin, feature_name))
                
                if not tasks_for_this_stage:
                    logger.info(f"RUN_ASYNC_V2: Stage {stage_name} - No pending (VIN, feature) tasks. Ending stages early.")
                    break
                
                logger.debug(f"RUN_ASYNC_V2: Stage {stage_name} - Total (VIN, feature) tasks for this stage: {len(tasks_for_this_stage)}. Sample: {tasks_for_this_stage[:10]}")

                duration_spec = stage_config["duration_spec"]
                retries_for_stage = stage_config["retries"]
                
                from_time_offset_ms = 0
                if "days" in duration_spec:
                    from_time_offset_ms = duration_spec["days"] * 24 * 60 * 60 * 1000
                elif "hours" in duration_spec:
                    from_time_offset_ms = duration_spec["hours"] * 60 * 60 * 1000
                else:
                    logger.error(f"RUN_ASYNC_V2: Invalid duration_spec in stage {stage_name}. Skipping stage.")
                    continue
                
                stage_from_time = current_to_time - from_time_offset_ms

                logger.info(f"RUN_ASYNC_V2: Starting data fetch for stage: {stage_name}. Retries: {retries_for_stage}.")
                logger.debug(f"RUN_ASYNC_V2: Time window for stage {stage_name}: {datetime.fromtimestamp(stage_from_time/1000)} to {datetime.fromtimestamp(current_to_time/1000)}")

                # `async_batch_fetch_all` will be refactored to accept `tasks_for_this_stage`
                # and return results mapped to (vin, feature_name)
                # For now, let's assume it's refactored or we call a new method `execute_stage_fetches`
                # The current `async_batch_fetch_all` takes `vin_numbers` (a list) not `tasks_for_this_stage`
                # This part will require the refactoring of `async_batch_fetch_all` next.
                # For now, this is a placeholder for the call:
                
                # This call needs to be to the refactored async_batch_fetch_all or a new method
                # that understands `tasks_for_this_stage` (list of (vin, feature_name) tuples)
                stage_run_results = await self.async_batch_fetch_all_v2( # Placeholder for new/refactored method
                    tasks_for_this_stage,
                    stage_from_time,
                    current_to_time,
                    retries_for_stage
                )
                
                logger.debug(f"RUN_ASYNC_V2: Stage {stage_name} results received. Processing hits...")
                
                successful_fetches_this_stage = 0
                if stage_run_results: # Expecting a list of (vin, feature_name, data_or_exception)
                    for vin, feature_name, data_from_fetch in stage_run_results: # Renamed 'data' to 'data_from_fetch' for clarity
                        if not isinstance(data_from_fetch, Exception) and self._is_actual_data(vin, feature_name, data_from_fetch): # Check for ACTUAL data
                            successful_fetches_this_stage += 1
                            # Store if not already found (first successful hit for this VIN/feature wins)
                            if vin not in aggregated_results.get(feature_name, {}):
                                if feature_name not in aggregated_results: aggregated_results[feature_name] = {} # Ensure feature key exists
                                aggregated_results[feature_name][vin] = data_from_fetch
                            
                            # Remove from pending_vin_features ONLY if actual data was found
                            if vin in pending_vin_features and feature_name in pending_vin_features[vin]:
                                pending_vin_features[vin].remove(feature_name)
                                if not pending_vin_features[vin]: # If no more features for this VIN
                                    del pending_vin_features[vin]
                        
                        elif not isinstance(data_from_fetch, Exception) and data_from_fetch is not None:
                            # This case means API responded successfully (e.g. HTTP 200) but _is_actual_data returned False
                            # (e.g. {"data": null} or {"data": []})
                            logger.info(f"RUN_ASYNC_V2: Stage {stage_name} - Received API response but no actual data for {feature_name} for VIN {vin} (e.g., empty data list or null payload). Will remain pending.")
                        
                        elif isinstance(data_from_fetch, Exception):
                            logger.warning(f"RUN_ASYNC_V2: Stage {stage_name} - Error fetching {feature_name} for VIN {vin}: {data_from_fetch}")
                        
                        # If data_from_fetch is None (e.g. from HTTP 204 or initial fetch failure before response),
                        # _is_actual_data(..., None) would have returned False.
                        # The (VIN, feature) pair correctly remains pending without needing an explicit 'else' here for that specific None case.
                
                logger.info(f"RUN_ASYNC_V2: Stage {stage_name} - Successful fetches in this stage: {successful_fetches_this_stage}")
                remaining_tasks_count = sum(len(features) for features in pending_vin_features.values())
                logger.debug(f"RUN_ASYNC_V2: Stage {stage_name} - Total (VIN, feature) pairs still pending globally: {remaining_tasks_count}")
                sample_remaining_pending = {k: list(v) for k, v in list(pending_vin_features.items())[:5] if v}
                logger.debug(f"RUN_ASYNC_V2: Stage {stage_name} - Sample remaining pending_vin_features: {sample_remaining_pending}")
                logger.info(f"RUN_ASYNC_V2: ----- STAGE {i+1}/{len(self.STAGES_CONFIG)} ({stage_name}) LOOP END -----")

            logger.info("RUN_ASYNC_V2: All stages completed or no pending tasks. Saving aggregated results...")
            self.save_pickle_data(aggregated_results)
            
            logger.info("RUN_ASYNC_V2: Creating/updating Google Sheets with aggregated results...")
            self.update_or_create_result_google_sheet(aggregated_results, self.master_sheet_id)
            
            logger.info("RUN_ASYNC_V2: Process completed successfully!")
            
        except Exception as e:
            logger.error(f"RUN_ASYNC_V2: Error in async run: {str(e)}", exc_info=True)
            raise

    def set_feature_flags(self, flags: dict):
        """Update feature flags - can be called by developers to control which features are enabled"""
        self.feature_flags.update(flags)
        logger.info(f"Feature flags updated: {self.feature_flags}")

    def get_feature_flags(self) -> dict:
        """Get current feature flags configuration"""
        return self.feature_flags.copy()

    def _is_actual_data(self, vin: str, feature_name: str, response_data: Optional[Dict]) -> bool:
        """
        Checks if the API response contains actual, usable data points.
        Returns True if actual data is present, False otherwise (e.g., HTTP 204, {"data": null}, {"data": []}).
        """
        if response_data is None: # Handles cases where async_fetch_with_retry returns None (e.g., HTTP 204)
            logger.debug(f"NO_ACTUAL_DATA for VIN {vin}, feature {feature_name}: Response data is None (API likely returned 204 or fetch failed before response).")
            return False
        
        data_payload = response_data.get('data')

        if data_payload is None: # Handles {"data": null}
            logger.debug(f"NO_ACTUAL_DATA for VIN {vin}, feature {feature_name}: 'data' key is missing or payload is None. Response: {str(response_data)[:200]}")
            return False

        if isinstance(data_payload, list) and not data_payload: # Handles {"data": []}
            logger.debug(f"NO_ACTUAL_DATA for VIN {vin}, feature {feature_name}: 'data' key is an empty list. Response: {str(response_data)[:200]}")
            return False
        
        # If we've reached here, data_payload is not None and (if a list) is not empty.
        logger.debug(f"ACTUAL_DATA_FOUND for VIN {vin}, feature {feature_name}. Response: {str(response_data)[:200]}")
        return True

    def authenticate_google_drive(self):
        creds = None
        token_path = 'auth/token.pickle'

        if os.path.exists(token_path):
            with open(token_path, 'rb') as token:
                creds = pickle.load(token)
        
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    'auth/auth.json', self.SCOPES)
                creds = flow.run_local_server(port=0)
            
            with open(token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        return creds

    def read_vin_numbers(self, csv_file):
        df = pd.read_csv(csv_file)
        return list(set(df['VIN'].tolist()))

    # CHANGE TIME INTERVAL HERE - DATETIME
    def fetch_network_timestamp(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/networktimestamp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching data for VIN {vin}: {str(e)}")
            return None

    def fetch_connectivity_data(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getNetworkData"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching connectivity data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching connectivity data for VIN {vin}: {str(e)}")
            return None
    
    def fetch_gps_timestamp(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/timerange/gpstimestamp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching GPS timestamp for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching GPS timestamp for VIN {vin}: {str(e)}")
            return None

    def fetch_horizontal_accuracy(self, vin, from_time, to_time, percentile=50):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/horizontalaccuracy"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&percentile={percentile}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, percentile={percentile}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching horizontal accuracy for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching horizontal accuracy for VIN {vin}: {str(e)}")
            return None

    def fetch_rsrp(self, vin, from_time, to_time, percentile=75):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/connectivityreport/percentile/rsrp"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&percentile={percentile}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, percentile={percentile}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching RSRP for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching RSRP for VIN {vin}: {str(e)}")
            return None

    def fetch_vcu_replacement(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/vehicledatausage/v2/getVCUReplacementDetailsVin"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching VCU replacement for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching VCU replacement for VIN {vin}: {str(e)}")
            return None

    def fetch_faults(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/faultdata"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching faults for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching faults for VIN {vin}: {str(e)}")
            return None

    def fetch_mcu_data(self, vin, from_time, to_time, bin_size=600):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/mcudata"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&binSize={bin_size}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, bin_size={bin_size}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching MCU data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching MCU data for VIN {vin}: {str(e)}")
            return None

    def fetch_bms_data(self, vin, from_time, to_time, bin_size=600):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/bmsdata"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&binSize={bin_size}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, bin_size={bin_size}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching BMS data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching BMS data for VIN {vin}: {str(e)}")
            return None

    def fetch_bcm_data(self, vin, from_time, to_time, bin_size=600):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/bcmdatalist"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&binSize={bin_size}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, bin_size={bin_size}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching BCM data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching BCM data for VIN {vin}: {str(e)}")
            return None

    def fetch_vehicle_mode_data(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/vehiclemodedata"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching vehicle mode data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching vehicle mode data for VIN {vin}: {str(e)}")
            return None

    def fetch_version_data(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/versionData"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching version data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching version data for VIN {vin}: {str(e)}")
            return None

    def fetch_scooter_state_data(self, vin, from_time, to_time, bin_size=600):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/mcudata/noncte_scooterstatechange"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}&binSize={bin_size}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}, bin_size={bin_size}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching scooter state data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching scooter state data for VIN {vin}: {str(e)}")
            return None

    def fetch_hpe_data(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/hpeData"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching HPE data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching HPE data for VIN {vin}: {str(e)}")
            return None

    def fetch_lpe_data(self, vin, from_time, to_time):
        base_url = "https://command-center-8080a.corp.olaelectric.com/api/proxy/nextgentelematics/adxtelematics/v2/getLpeData"
        url = f"{base_url}?vehicleId={vin}&fromTime={from_time}&toTime={to_time}"
        logger.info(f"Calling endpoint: {url} [VIN={vin}, from_time={from_time}, to_time={to_time}]")
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error fetching LPE data for VIN {vin}: Status code {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception while fetching LPE data for VIN {vin}: {str(e)}")
            return None

    def process_network_data(self, data):
        if not data or 'data' not in data:
            return {
                "vin": None,
                "timestamp": None,
                "status_code": data.get('statusCode') if data else None,
                "message": data.get('message') if data else "No data available",
                "total_records": 0,
                "data_available": False
            }
        try:
            vehicle_data = data['data'][0] if data['data'] else None
            if not vehicle_data:
                return {
                    "vin": None,
                    "timestamp": None,
                    "status_code": data.get('statusCode'),
                    "message": data.get('message', "No vehicle data found"),
                    "total_records": 0,
                    "data_available": False
                }
                
            return {
                "vin": vehicle_data.get('vehicle_identification_number'),
                "timestamp": vehicle_data.get('timestamp'),
                "status_code": data.get('statusCode'),
                "message": data.get('message'),
                "total_records": data.get('totalRecordCount'),
                "data_available": True
            }
        except Exception as e:
            print(f"Error processing data: {str(e)}")
            return {
                "vin": None,
                "timestamp": None,
                "status_code": None,
                "message": f"Error processing data: {str(e)}",
                "total_records": 0,
                "data_available": False
            }

    def convert_timestamp(self, timestamp: str) -> str:
        """Convert timestamp to human readable format"""
        try:
            # Handle millisecond timestamps
            if timestamp and timestamp.isdigit():
                return datetime.fromtimestamp(int(timestamp) / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            # Handle ISO format timestamps
            elif timestamp:
                return timestamp
            return ''
        except Exception as e:
            print(f"Error converting timestamp {timestamp}: {str(e)}")
            return timestamp or ''

    def format_network_timestamp_data(self, vin: str, data: Dict) -> list:
        """Format network timestamp data for spreadsheet"""
        if data is None:
            return [vin, '', '', 'No Response', 'No data available', 0, 'No']
        
        timestamp = ''
        if data.get('data') and len(data['data']) > 0:
            timestamp = data['data'][0].get('timestamp', '')
        
        return [
            vin,
            timestamp,
            self.convert_timestamp(timestamp),
            data.get('statusCode', 'No Status Code'),
            data.get('message', 'No Message'),
            data.get('totalRecordCount', 0),
            'Yes' if data.get('data', []) else 'No'
        ]

    def format_connectivity_data(self, vin: str, data: Dict) -> list:
        """Format connectivity data for spreadsheet based on actual response"""
        try:
            if data is None:
                return [vin, '', '', '', '', '']
            
            conn_data = {}
            if data.get('data') and len(data['data']) > 0:
                conn_data = data['data'][0]
            
            return [
                vin,
                conn_data.get('timestamp', ''),
                conn_data.get('iccid', ''),
                conn_data.get('scooter_state', ''),
                conn_data.get('signal_rsrp', ''),
                conn_data.get('signal_strength', '')
            ]
        except Exception as e:
            print(f"Error in format_connectivity_data for VIN {vin}: {str(e)}")
            return [vin, '', '', '', '', '']

    def format_gps_data(self, vin: str, data: Dict) -> list:
        """Format GPS data for spreadsheet based on actual response"""
        try:
            if data is None:
                return [vin, '', '', '', '', '']
            
            gps_data = {}
            if data.get('data') and len(data['data']) > 0:
                gps_data = data['data'][0]
            
            return [
                vin,
                gps_data.get('timestamp', ''),
                gps_data.get('latitude', ''),
                gps_data.get('longitude', ''),
                gps_data.get('horizontal_accuracy', ''),
                gps_data.get('location', '')
            ]
        except Exception as e:
            print(f"Error in format_gps_data for VIN {vin}: {str(e)}")
            return [vin, '', '', '', '', '']

    def format_rsrp_data(self, vin: str, data: Dict) -> list:
        """Format RSRP data for spreadsheet based on actual response"""
        try:
            if data is None:
                return [vin, '', '', '', '', 75]
            
            rsrp_data = {}
            if data.get('data') and len(data['data']) > 0:
                rsrp_data = data['data'][0]
            
            raw_timestamp = data.get('timestamp', '')
            return [
                vin,
                raw_timestamp,
                self.convert_timestamp(raw_timestamp),
                rsrp_data.get('percentile_1', ''),
                rsrp_data.get('signal_rsrp', ''),
                75
            ]
        except Exception as e:
            print(f"Error in format_rsrp_data for VIN {vin}: {str(e)}")
            return [vin, '', '', '', '', 75]

    def format_horizontal_accuracy_data(self, vin: str, data: Dict) -> list:
        """Format horizontal accuracy data for spreadsheet based on actual response"""
        try:
            if data is None:
                return [vin, '', '', '', '', 50]
            
            horacc_data = {}
            if data.get('data') and len(data['data']) > 0:
                horacc_data = data['data'][0]
            
            raw_timestamp = data.get('timestamp', '')
            return [
                vin,
                raw_timestamp,
                self.convert_timestamp(raw_timestamp),
                horacc_data.get('percentile_1', ''),
                horacc_data.get('horacc', ''),
                50
            ]
        except Exception as e:
            print(f"Error in format_horizontal_accuracy_data for VIN {vin}: {str(e)}")
            return [vin, '', '', '', '', 50]

    def format_version_info_data(self, vin: str, data: Dict) -> list:
        """Format version info data for spreadsheet, including BMS, BCM, HU, MCU sw_bsw_version"""
        try:
            # Handle None data case
            if data is None:
                return [vin] + [''] * 21  # VIN plus 21 empty fields (4 new columns)
            # Single Element List
            version_data = data.get('data', [{}])[0] if data.get('data') else {}
            ecu_version_str = version_data.get('ecu_version', '')
            ecu_version_str = ecu_version_str.replace('\\', '')
            ecu_version_str = ecu_version_str.replace('{"{"', '{"')
            ecu_version_str = ecu_version_str.replace('"}"}', '"}')
            # Parse ecu_version JSONs for sw_bsw_version by ecu_type
            bms_sw, bcm_sw, hu_sw, mcu_sw = '', '', '', ''
            if ecu_version_str:
                import re
                import json
                # pattern of ecu_version_str is {"ecu_type":"MASTER_BMS","sw_bsw_version":"1.0.0.0"}
                pattern = r'\{".*?\"}'
                ecu_jsons = re.findall(pattern, ecu_version_str)
                for ecu_json in ecu_jsons:
                    try:
                        ecu = json.loads(ecu_json)
                        ecu_type = ecu.get('ecu_type', '').upper()
                        sw_bsw = ecu.get('sw_bsw_version', '')
                        if ecu_type == 'MASTER_BMS':
                            bms_sw = sw_bsw
                        elif ecu_type == 'BCM':
                            bcm_sw = sw_bsw
                        elif ecu_type == 'HU':
                            hu_sw = sw_bsw
                        elif ecu_type == 'MCU':
                            mcu_sw = sw_bsw
                    except Exception:
                        continue
            return [
                vin,
                version_data.get('vehicle_software_version', ''),
                version_data.get('move_os_version', ''),
                version_data.get('platform_config_version', ''),
                version_data.get('app_config_version', ''),
                version_data.get('services_config_version', ''),
                version_data.get('ecu_config_version', ''),
                version_data.get('bcm_ble_version', ''),
                version_data.get('hmi_version', ''),
                version_data.get('otam_version', ''),
                version_data.get('firmware_version', ''),
                version_data.get('hardware_version', ''),
                version_data.get('scooter_variant', ''),
                version_data.get('battery_id', ''),
                version_data.get('scooter_id', ''),
                version_data.get('som_id', ''),
                version_data.get('timestamp', ''),
                self.convert_timestamp(version_data.get('timestamp', '')),
                bms_sw,
                bcm_sw,
                hu_sw,
                mcu_sw
            ]
        except Exception as e:
            print(f"Error in format_version_info_data for VIN {vin}: {str(e)}")
            return [vin] + [''] * 21

    def format_last_active_info_data(self, vin: str, data: Optional[Dict]) -> list:
        """Formats data from async_fetch_last_active_info for Google Sheets."""
        if data and data.get("data_available") == "Yes":
            # Assuming 'timestamp' is the raw timestamp string like "2025-04-17 11:03:34"
            # from getVehicleInfoBMS, which is already in human-readable format.
            raw_timestamp = data.get('timestamp', '')
            # The raw_timestamp is already in the desired "YYYY-MM-DD HH:MM:SS" format.
            # So, the "converted" timestamp will be the same.
            converted_timestamp = raw_timestamp
            return [
                vin,
                raw_timestamp,
                converted_timestamp, # Will be same as raw_timestamp as it's already formatted
                data.get('pack_soc', ''),
                data.get('scooter_state', ''),
                data.get('time_charge_full_sc_1', ''),
                data.get('status_code', ''),
                data.get('message', ''),
                "Yes"
            ]
        else:
            # Handle cases where data is None or not available
            status_code = data.get('status_code', 'N/A') if data else 'N/A'
            message = data.get('message', 'No data available or error') if data else 'No data available or error'
            return [
                vin, '', '', '', '', '',
                status_code, message, "No"
            ]

    def get_default_row(self, sheet_name: str, vin: str) -> list:
        """Return default row with empty values based on sheet type, updated for Version Info column count"""
        if sheet_name == 'Network Timestamp':
            return [vin, '', '', 'No Response', 'No data available', 0, 'No']
        elif sheet_name == 'Connectivity':
            return [vin, '', '', '', '', '']
        elif sheet_name == 'GPS':
            return [vin, '', '', '', '', '']
        elif sheet_name == 'RSRP':
            return [vin, '', '', '', '', 75]
        elif sheet_name == 'Horizontal Accuracy':
            return [vin, '', '', '', '', 50]
        elif sheet_name == 'Version Info':
            return [vin] + [''] * 21
        elif sheet_name == 'Last Active Info':
            # VIN, Last Active Timestamp (Raw), Last Active Timestamp (Converted), Pack SOC, Scooter State, Time Charge Full SC 1, Status Code, Message, Data Available
            return [vin, '', '', '', '', '', 'N/A', 'No data retrieved', 'No']
        else:
            return [vin, 'No data']
            
    def option_selection(self, list_options):
        for i, option in enumerate(list_options):
            print(f"{i+1}. {option}")
        choice = input("Enter the number of the option you want to select: ")
        return list_options[int(choice) - 1]

    def save_results(self):
        # Save results to JSON
        with open('network_timestamp_results.json', 'w') as f:
            json.dump(self.results, f, indent=4)
        
        # Convert results to DataFrame and save as Excel
        if self.results:
            df = pd.DataFrame(self.results)
            file_name = f'results/network_timestamp_results_{datetime.now().strftime("%Y-%m-%d %H_%M")}.xlsx'
            pathlib.Path(file_name).parent.mkdir(parents=True, exist_ok=True)
            df.to_excel(file_name, index=False)
            print(f"\nResults saved to {file_name}")
            
            # Create Google Spreadsheet
            spreadsheet_id = self.update_or_create_result_google_sheet()
            if spreadsheet_id:
                print(f"Results also saved to Google Spreadsheet with ID: {spreadsheet_id}")
                print(f"Spreadsheet URL: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
        else:
            print("\nNo data to save. Please check the API responses.")

    def test_run_network_connect_gps_horizontal_rsrp(self, vin="P53BGDCAXDCA00005"):
        start_time, end_time = self.get_time_range()
        self.fetch_connectivity_data(vin, start_time, end_time)
        self.fetch_gps_timestamp(vin, start_time, end_time)
        self.fetch_rsrp(vin, start_time, end_time)
        breakpoint()

    def update_or_create_result_google_sheet(self, all_results: Dict[str, Dict[str, Any]], spreadsheet_id: str = None):
        """Create a Google Sheet with multiple sheets for different data types based on feature flags.
        If a VIN has no new data, retain its previous row from the sheet."""
        try:
            creds = self.authenticate_google_drive()
            service = build('sheets', 'v4', credentials=creds)

            # Map feature names to sheet names and their configurations
            sheet_configs = {}
            enabled_sheets = []
            
            # Network Timestamp configuration
            if self.feature_flags.get('network_timestamp'):
                sheet_name = 'Network Timestamp'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': ['VIN', 'Raw Timestamp', 'Human Readable Timestamp', 'Status Code', 'Message', 'Total Records', 'Data Available'],
                    'data': all_results.get('network_timestamp', {})
                }
            
            # Connectivity configuration
            if self.feature_flags.get('connectivity'):
                sheet_name = 'Connectivity'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': ['VIN', 'Timestamp', 'ICCID', 'Scooter State', 'Signal RSRP', 'Signal Strength'],
                    'data': all_results.get('connectivity', {})
                }
            
            # GPS configuration
            if self.feature_flags.get('gps'):
                sheet_name = 'GPS'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': ['VIN', 'Timestamp', 'Latitude', 'Longitude', 'Horizontal Accuracy', 'Location'],
                    'data': all_results.get('gps', {})
                }
            
            # RSRP configuration
            if self.feature_flags.get('rsrp'):
                sheet_name = 'RSRP'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': ['VIN', 'Raw Timestamp', 'Human Readable Timestamp', 'RSRP Value', 'Signal Status', 'Percentile'],
                    'data': all_results.get('rsrp', {})
                }
            
            # Horizontal Accuracy configuration
            if self.feature_flags.get('horizontal_accuracy'):
                sheet_name = 'Horizontal Accuracy'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': ['VIN', 'Raw Timestamp', 'Human Readable Timestamp', 'Accuracy Value', 'Status', 'Percentile'],
                    'data': all_results.get('horizontal_accuracy', {})
                }
            
            # Version Info configuration
            if self.feature_flags.get('version_info'):
                sheet_name = 'Version Info'
                enabled_sheets.append({'properties': {'title': sheet_name}})
                sheet_configs[sheet_name] = {
                    'headers': [
                        'VIN', 'Vehicle Software Version', 'Move OS Version', 'Platform Config Version',
                        'App Config Version', 'Services Config Version', 'ECU Config Version',
                        'BCM BLE Version', 'HMI Version', 'OTAM Version', 'Firmware Version',
                        'Hardware Version', 'Scooter Variant', 'Battery ID', 'Scooter ID',
                        'SOM ID', 'Raw Timestamp', 'Human Readable Timestamp',
                        'BMS SW_BSW Version', 'BCM SW_BSW Version', 'HU SW_BSW Version', 'MCU SW_BSW Version'
                    ],
                    'data': all_results.get('version_info', {})
                }
            
            # Last Active Info configuration
            # This feature is primary and not controlled by feature_flags in the same way as secondary features.
            # We assume it's always attempted if async_simple.py is run.
            # The 'last_active_info' key in all_results will be populated by async_simple.py
            sheet_name_last_active = 'Last Active Info'
            # Ensure it's added to enabled_sheets if not already (e.g. if spreadsheet is new)
            if not any(s['properties']['title'] == sheet_name_last_active for s in enabled_sheets):
                 enabled_sheets.append({'properties': {'title': sheet_name_last_active}})

            sheet_configs[sheet_name_last_active] = {
                'headers': ['VIN', 'Last Active Timestamp (Raw)', 'Last Active Timestamp (Converted)', 'Pack SOC', 'Scooter State', 'Time Charge Full SC 1', 'Status Code', 'Message', 'Data Available'],
                'data': all_results.get('last_active_info', {}) # Data comes from 'last_active_info' key
            }

            if not enabled_sheets:
                logger.warning("No sheets enabled. Skipping Google Sheets update.")
                return None

            # Create or update spreadsheet
            if spreadsheet_id is None:
                spreadsheet_config = {
                    'properties': {
                        'title': f'Vehicle Data Results {datetime.now().strftime("%Y-%m-%d %H_%M")}'
                    },
                    'sheets': enabled_sheets
                }
                spreadsheet = service.spreadsheets().create(body=spreadsheet_config).execute()
                spreadsheet_id = spreadsheet['spreadsheetId']
            else:
                # Get existing sheets
                spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet.get('sheets', [])]
                
                # Add any missing sheets
                requests = []
                for sheet in enabled_sheets:
                    if sheet['properties']['title'] not in existing_sheets:
                        requests.append({'addSheet': sheet})
                
                if requests:
                    service.spreadsheets().batchUpdate(
                        spreadsheetId=spreadsheet_id,
                        body={'requests': requests}
                    ).execute()

            # Update each enabled sheet
            for sheet_name, config in sheet_configs.items():
                # 1. Fetch current sheet data (excluding header)
                prev_rows = self.get_google_sheet(spreadsheet_id, sheet_name)
                prev_data_rows = prev_rows[1:] if prev_rows and len(prev_rows) > 1 else []
                # Build VIN to row mapping for previous data
                prev_vin_to_row = {row[0]: row for row in prev_data_rows if row and len(row) > 0}
                # 2. Build set of all VINs (union of previous and new)
                new_vins = set(config['data'].keys())
                all_vins = set(prev_vin_to_row.keys()).union(new_vins)
                # 3. Build new rows
                rows = [config['headers']]
                for vin in all_vins:
                    data = config['data'].get(vin)
                    try:
                        if data is not None:
                            # Use new data
                            if sheet_name == 'Network Timestamp':
                                row = self.format_network_timestamp_data(vin, data)
                            elif sheet_name == 'Connectivity':
                                row = self.format_connectivity_data(vin, data)
                            elif sheet_name == 'GPS':
                                row = self.format_gps_data(vin, data)
                            elif sheet_name == 'RSRP':
                                row = self.format_rsrp_data(vin, data)
                            elif sheet_name == 'Horizontal Accuracy':
                                row = self.format_horizontal_accuracy_data(vin, data)
                            elif sheet_name == 'Version Info':
                                row = self.format_version_info_data(vin, data)
                            elif sheet_name == 'Last Active Info':
                                row = self.format_last_active_info_data(vin, data)
                            else:
                                row = self.get_default_row(sheet_name, vin)
                        elif vin in prev_vin_to_row:
                            # Use previous row
                            row = prev_vin_to_row[vin]
                        else:
                            # Use default row
                            row = self.get_default_row(sheet_name, vin)
                        rows.append(row)
                    except Exception as e:
                        logger.error(f"Error formatting {sheet_name} data for VIN {vin}: {str(e)}")
                        if vin in prev_vin_to_row:
                            rows.append(prev_vin_to_row[vin])
                        else:
                            rows.append(self.get_default_row(sheet_name, vin))
                        continue

                range_name = f"'{sheet_name}'!A1"
                body = {'values': rows}
                service.spreadsheets().values().update(
                    spreadsheetId=spreadsheet_id,
                    range=range_name,
                    valueInputOption='RAW',
                    body=body
                ).execute()

            logger.info(f"Google Spreadsheet updated successfully. ID: {spreadsheet_id}")
            logger.info(f"Spreadsheet URL: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
            return spreadsheet_id

        except Exception as e:
            logger.error(f"Error creating/updating Google Spreadsheet: {str(e)}")
            return None

    def get_google_sheet(self, spreadsheet_id: str, sheet_name: str):
        """Get a specific sheet from Google Spreadsheet"""
        try:
            creds = self.authenticate_google_drive()
            service = build('sheets', 'v4', credentials=creds)
            
            # Get all sheets in the spreadsheet
            spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            sheets = spreadsheet.get('sheets', [])
            # Find the sheet ID for the requested sheet name
            sheet_id = None
            for sheet in sheets:
                if sheet['properties']['title'] == sheet_name:
                    sheet_id = sheet['properties']['sheetId']
                    break
            
            if sheet_id is None:
                print(f"Sheet '{sheet_name}' not found in spreadsheet")
                return None
            
            # Get the sheet data
            result = service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=sheet_name
            ).execute()
            # get values to return
            return result.get('values', [])
        except Exception as e:
            print(f"Error getting Google Sheet: {str(e)}")
            return None

async def check_versions(vin: str):
    checker = NetworkTimestampChecker()
    session = aiohttp.ClientSession()
    network_data = await checker.async_fetch_version_info(session=session, vin=vin)
    return network_data

if __name__ == "__main__":
    checker = NetworkTimestampChecker()
    # Run Now Code
    checker.set_feature_flags({
        'network_timestamp': False,
        'connectivity': True,
        'gps': True,
        'rsrp': False,
        'horizontal_accuracy': False,
        'version_info': True
    })
    # run the async main function
    asyncio.run(checker.run_async())

    # Version Info Test
    # network_data = asyncio.run(check_versions("P53BXDCCXEAA00018"))
    # # Formatted Version Info
    # formatted_version_info = checker.format_version_info_data("P53BXDCCXEAA00018", network_data)
    # print(formatted_version_info)
    # breakpoint()